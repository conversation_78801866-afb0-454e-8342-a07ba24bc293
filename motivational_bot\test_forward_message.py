#!/usr/bin/env python3
"""
Test forwarding a message from channel to user
"""

import os
import asyncio
import logging
from dotenv import load_dotenv
from telegram import Bot

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)

logger = logging.getLogger(__name__)

async def test_forward():
    """Test forwarding a message"""
    
    bot_token = os.getenv('BOT_TOKEN')
    if not bot_token:
        logger.error("BOT_TOKEN not found!")
        return
    
    bot = Bot(token=bot_token)
    
    # Channel info
    channel_id = "@angizeshivahid"  # Your channel
    message_id = 2000  # A recent message ID
    
    # Your chat ID (replace with your actual chat ID)
    your_chat_id = 1234567890  # Replace with your actual chat ID
    
    print(f"""
🧪 Testing Message Forward
━━━━━━━━━━━━━━━━━━━━
📺 Channel: {channel_id}
📝 Message ID: {message_id}
👤 Target: {your_chat_id}
    """)
    
    try:
        # Try to forward the message
        forwarded_message = await bot.forward_message(
            chat_id=your_chat_id,
            from_chat_id=channel_id,
            message_id=message_id
        )
        
        print(f"✅ Message forwarded successfully!")
        print(f"📤 Forwarded message ID: {forwarded_message.message_id}")
        
    except Exception as e:
        print(f"❌ Error forwarding message: {e}")
        
        # Try to get channel info
        try:
            chat = await bot.get_chat(chat_id=channel_id)
            print(f"📋 Channel info: {chat.title} (ID: {chat.id})")
        except Exception as e2:
            print(f"❌ Error getting channel info: {e2}")

if __name__ == "__main__":
    print("⚠️ IMPORTANT: Replace 'your_chat_id' with your actual chat ID!")
    print("To get your chat ID, send a message to @userinfobot")
    print()
    
    # Uncomment the line below after setting your chat ID
    # asyncio.run(test_forward())
    print("Edit this file and set your chat ID first!")
