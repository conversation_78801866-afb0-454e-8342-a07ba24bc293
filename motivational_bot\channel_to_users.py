#!/usr/bin/env python3
"""
Send message to channel first, then forward to database users
"""

import os
import sys
import asyncio
import logging
from datetime import datetime
from dotenv import load_dotenv
from telegram import Bo<PERSON>

from database import DatabaseManager

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)

logger = logging.getLogger(__name__)

async def send_channel_to_users():
    """Send message to channel first, then forward to all database users"""
    
    bot_token = os.getenv('BOT_TOKEN')
    channel_id = os.getenv('CHANNEL_ID')
    
    if not bot_token:
        logger.error("BOT_TOKEN not found!")
        return False
    
    if not channel_id:
        logger.error("CHANNEL_ID not found!")
        return False
    
    logger.info(f"🚀 Starting channel-to-users message flow...")
    logger.info(f"📺 Channel: {channel_id}")
    
    # Initialize components
    db_manager = DatabaseManager()
    bot = Bot(token=bot_token)
    
    # Get active users from database
    active_users = db_manager.get_active_users()
    logger.info(f"👥 Found {len(active_users)} active users in database")
    
    if not active_users:
        logger.warning("⚠️ No active users found in database!")
        return False
    
    # Get random content
    content = db_manager.get_random_content()
    if not content:
        logger.error("❌ No content found in database!")
        return False
    
    content_id, content_type, text, file_path, file_id = content
    logger.info(f"📝 Content selected: {text[:50]}...")
    
    # Create beautiful channel message
    current_time = datetime.now()
    time_str = current_time.strftime("%H:%M")
    date_str = current_time.strftime("%Y/%m/%d")
    day_name = current_time.strftime("%A")
    
    # Persian day names
    persian_days = {
        'Monday': 'دوشنبه',
        'Tuesday': 'سه‌شنبه', 
        'Wednesday': 'چهارشنبه',
        'Thursday': 'پنج‌شنبه',
        'Friday': 'جمعه',
        'Saturday': 'شنبه',
        'Sunday': 'یکشنبه'
    }
    
    persian_day = persian_days.get(day_name, day_name)
    
    # Beautiful message format for channel
    message_text = f"""🌟 پیام انگیزشی روزانه

{text}

━━━━━━━━━━━━━━━━━━━━
📅 {persian_day} | {date_str}
🕐 {time_str}
💫 کانال انگیزشی واحد

#انگیزشی #موفقیت #روزانه #پیام_روز"""

    try:
        # Step 1: Send message to channel
        logger.info("📺 Step 1: Sending message to channel...")
        
        if content_type == 'text':
            channel_message = await bot.send_message(
                chat_id=channel_id,
                text=message_text,
                parse_mode='HTML'
            )
        elif content_type == 'photo' and file_path:
            channel_message = await bot.send_photo(
                chat_id=channel_id,
                photo=file_path,
                caption=message_text,
                parse_mode='HTML'
            )
        elif content_type == 'video' and file_path:
            channel_message = await bot.send_video(
                chat_id=channel_id,
                video=file_path,
                caption=message_text,
                parse_mode='HTML'
            )
        elif content_type == 'audio' and file_path:
            channel_message = await bot.send_audio(
                chat_id=channel_id,
                audio=file_path,
                caption=message_text,
                parse_mode='HTML'
            )
        else:
            # Fallback to text
            channel_message = await bot.send_message(
                chat_id=channel_id,
                text=message_text,
                parse_mode='HTML'
            )
        
        logger.info(f"✅ Message posted to channel successfully!")
        logger.info(f"📱 Channel Message ID: {channel_message.message_id}")
        
        # Step 2: Forward channel message to all database users
        logger.info("👥 Step 2: Forwarding to database users...")
        
        success_count = 0
        failed_count = 0
        
        for user_id in active_users:
            try:
                # Forward the channel message to user
                forwarded_message = await bot.forward_message(
                    chat_id=user_id,
                    from_chat_id=channel_id,
                    message_id=channel_message.message_id
                )
                
                success_count += 1
                logger.info(f"✅ Message forwarded to user {user_id}")
                
                # Small delay to avoid rate limiting
                await asyncio.sleep(0.1)
                
            except Exception as e:
                failed_count += 1
                logger.error(f"❌ Failed to forward to user {user_id}: {e}")
                
                # Handle specific errors
                if "bot was blocked" in str(e).lower():
                    logger.info(f"🚫 User {user_id} blocked the bot - deactivating")
                    db_manager.deactivate_user(user_id)
                elif "chat not found" in str(e).lower():
                    logger.info(f"👻 User {user_id} not found - deactivating")
                    db_manager.deactivate_user(user_id)
        
        # Final results
        logger.info(f"🎯 Forwarding completed!")
        logger.info(f"✅ Success: {success_count}/{len(active_users)} users")
        logger.info(f"❌ Failed: {failed_count}/{len(active_users)} users")
        
        print(f"""
🎉 Channel-to-Users Message Flow Completed!

📺 Channel Message:
   ✅ Posted to: {channel_id}
   📱 Message ID: {channel_message.message_id}
   🕐 Time: {channel_message.date}

👥 User Forwarding:
   ✅ Successful: {success_count} users
   ❌ Failed: {failed_count} users
   📊 Total: {len(active_users)} users

🔗 Channel Link: https://t.me/angizeshivahid
        """)
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Failed to send to channel: {e}")
        
        # Common error solutions
        if "chat not found" in str(e).lower():
            logger.error("💡 Solution: Make sure the channel username is correct (@angizeshivahid)")
        elif "not enough rights" in str(e).lower():
            logger.error("💡 Solution: Add the bot as admin to the channel with 'Post Messages' permission")
        elif "bot was blocked" in str(e).lower():
            logger.error("💡 Solution: Unblock the bot or check bot permissions")
        
        return False

if __name__ == "__main__":
    print("""
🚀 Channel-to-Users Message Sender

This script will:
1. 📺 Post a motivational message to your channel (@angizeshivahid)
2. 👥 Forward that message to all active users in the database

This way users receive the message as if it came from the channel!
    """)
    
    asyncio.run(send_channel_to_users())
