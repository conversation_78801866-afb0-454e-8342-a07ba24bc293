#!/usr/bin/env python3
"""
Test scheduler - sends message in 1 minute for testing
"""

import os
import sys
import asyncio
import logging
from datetime import datetime, timedelta
from dotenv import load_dotenv

from bot import MotivationalBot
from database import DatabaseManager
from content_manager import ContentManager

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)

logger = logging.getLogger(__name__)

async def test_schedule():
    """Test scheduling with 1 minute delay"""
    
    bot_token = os.getenv('BOT_TOKEN')
    if not bot_token:
        logger.error("BOT_TOKEN not found!")
        return
    
    logger.info("🧪 Testing scheduler - message will be sent in 1 minute...")
    
    # Initialize components
    db_manager = DatabaseManager()
    content_manager = ContentManager(db_manager)
    
    # Initialize the application directly
    from telegram.ext import Application, CommandHandler, CallbackQueryHandler
    application = Application.builder().token(bot_token).build()
    
    # Create bot instance
    bot = MotivationalBot(bot_token)
    bot.application = application
    
    # Add handlers
    application.add_handler(CommandHandler("start", bot.start_command))
    application.add_handler(CommandHandler("subscribe", bot.subscribe_command))
    application.add_handler(CommandHandler("unsubscribe", bot.unsubscribe_command))
    application.add_handler(CommandHandler("status", bot.status_command))
    application.add_handler(CommandHandler("help", bot.help_command))
    application.add_handler(CallbackQueryHandler(bot.button_callback))
    
    # Initialize and start the application
    await application.initialize()
    await application.start()
    
    logger.info("✅ Bot initialized successfully!")
    
    # Check users
    active_users = db_manager.get_active_users()
    logger.info(f"📊 Active users: {len(active_users)}")
    
    if not active_users:
        logger.warning("⚠️ No active users! Please subscribe to the bot first.")
        logger.info("📱 Send /start and then /subscribe to the bot")
    
    # Start polling
    await application.updater.start_polling()
    
    # Wait 1 minute then send message
    logger.info("⏰ Waiting 1 minute before sending test message...")
    await asyncio.sleep(60)
    
    # Send test message
    logger.info("📤 Sending test message now...")
    await bot.send_daily_message()
    
    # Keep running for another minute
    logger.info("🔄 Bot will run for 1 more minute...")
    await asyncio.sleep(60)
    
    await application.stop()
    await application.shutdown()
    
    logger.info("🏁 Test completed!")

if __name__ == "__main__":
    print("""
🧪 Testing Scheduler - 1 Minute Test

This script will:
1. Start the bot
2. Wait 1 minute
3. Send a motivational message to all subscribers
4. Stop after another minute

Make sure to subscribe to the bot first!
    """)
    
    asyncio.run(test_schedule())
