#!/usr/bin/env python3
"""
Test the new system: read from channel tracker and send as bot messages
"""

import os
import asyncio
import logging
from datetime import datetime
from dotenv import load_dotenv
from telegram import Bo<PERSON>

from database import DatabaseManager
from channel_message_tracker import ChannelMessageTracker

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)

logger = logging.getLogger(__name__)

async def test_new_system():
    """Test the new system"""
    
    bot_token = os.getenv('BOT_TOKEN')
    
    if not bot_token:
        logger.error("BOT_TOKEN not found!")
        return
    
    print(f"""
🧪 Testing New System

This system:
1. 📖 Gets next message ID from channel tracker
2. 📝 Uses predefined content based on message ID
3. 🤖 Sends message FROM BOT to database users
4. ✅ Marks message as used

The message will come from the bot, not forwarded from channel.
    """)
    
    # Initialize components
    bot = Bot(token=bot_token)
    db = DatabaseManager()
    tracker = ChannelMessageTracker()
    
    # Get active users
    active_users = db.get_active_users()
    print(f"👥 Active users: {len(active_users)}")
    
    if not active_users:
        print("⚠️ No active users found!")
        return
    
    # Get next unused message
    unused_message = tracker.get_unused_message()
    if not unused_message:
        print("⚠️ No unused messages available!")
        return
    
    message_id, tracked_channel_id = unused_message
    print(f"📤 Processing message ID: {message_id}")
    
    # Get message content based on ID
    sample_messages = {
        248: "🌟 موفقیت نتیجه تلاش‌های کوچک روزانه است که هر روز تکرار می‌شوند.",
        249: "🏔️ کوه‌ها از سنگ‌های کوچک ساخته می‌شوند. هر قدم کوچک شما به سمت قله‌ای بزرگ است.",
        250: "💪 قدرت واقعی در این نیست که هرگز نیفتید، بلکه در این است که هر بار بلند شوید.",
        251: "🌅 هر روز فرصت جدیدی است برای شروع دوباره. امروز را با انگیزه آغاز کنید.",
        252: "🎯 هدف‌هایتان را واضح تعریف کنید و هر روز قدمی به سمت آن‌ها بردارید."
    }
    
    content_text = sample_messages.get(message_id, f"پیام انگیزشی شماره {message_id}")
    print(f"📝 Content: {content_text[:50]}...")
    
    # Create beautiful message format
    current_time = datetime.now()
    time_str = current_time.strftime("%H:%M")
    date_str = current_time.strftime("%Y/%m/%d")
    day_name = current_time.strftime("%A")
    
    # Persian day names
    persian_days = {
        'Monday': 'دوشنبه', 'Tuesday': 'سه‌شنبه', 'Wednesday': 'چهارشنبه',
        'Thursday': 'پنج‌شنبه', 'Friday': 'جمعه', 'Saturday': 'شنبه', 'Sunday': 'یکشنبه'
    }
    persian_day = persian_days.get(day_name, day_name)
    
    message_text = f"""🌟 پیام انگیزشی روزانه

{content_text}

━━━━━━━━━━━━━━━━━━━━
📅 {persian_day} | {date_str}
🕐 {time_str}
💫 از کانال انگیزشی واحد

#انگیزشی #موفقیت #روزانه"""

    try:
        success_count = 0
        failed_count = 0
        
        print(f"🚀 Sending message to users...")
        
        # Send to all users
        for user_id in active_users:
            try:
                await bot.send_message(
                    chat_id=user_id,
                    text=message_text,
                    parse_mode='HTML'
                )
                
                success_count += 1
                print(f"✅ Message sent to user {user_id}")
                
                # Small delay
                await asyncio.sleep(0.1)
                
            except Exception as e:
                failed_count += 1
                print(f"❌ Failed to send to user {user_id}: {e}")
        
        # Mark as used if successful
        if success_count > 0:
            tracker.mark_message_used(message_id)
            print(f"✅ Marked message {message_id} as used")
        
        print(f"""
🎯 Test Results:
   ✅ Successful sends: {success_count}
   ❌ Failed sends: {failed_count}
   📊 Total users: {len(active_users)}
        """)
        
        # Show updated stats
        stats = tracker.get_stats()
        print(f"📈 Updated Tracker Stats:")
        print(f"   Total messages: {stats.get('total_messages', 0)}")
        print(f"   Used messages: {stats.get('used_messages', 0)}")
        print(f"   Unused messages: {stats.get('unused_messages', 0)}")
        
        # Show next message
        next_unused = tracker.get_unused_message()
        if next_unused:
            next_id, _ = next_unused
            print(f"📤 Next message for tomorrow: {next_id}")
        else:
            print(f"⚠️ No more unused messages!")
        
        print(f"""
✅ Test Complete!

The message was sent FROM THE BOT (not forwarded from channel).
Users received it as a regular bot message with channel content.

This is the new system:
- 🤖 Message comes from bot
- 📝 Content is based on channel messages
- 👥 Sent to database users only
- 🔄 No forwarding involved
        """)
        
    except Exception as e:
        logger.error(f"Error in test: {e}")

if __name__ == "__main__":
    asyncio.run(test_new_system())
