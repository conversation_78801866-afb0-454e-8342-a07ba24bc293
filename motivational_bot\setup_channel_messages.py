#!/usr/bin/env python3
"""
Setup channel messages for daily forwarding
Add your channel message IDs to the tracker
"""

import os
import asyncio
import logging
from dotenv import load_dotenv
from channel_message_tracker import ChannelMessageTracker

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)

logger = logging.getLogger(__name__)

async def setup_messages():
    """Setup channel messages for forwarding"""
    
    channel_id = os.getenv('CHANNEL_ID', '@angizeshivahid')
    tracker = ChannelMessageTracker()
    
    print(f"""
🔧 Channel Message Setup

This script helps you add channel message IDs to the tracker
so the bot can forward them daily to subscribers.

Channel: {channel_id}

Instructions:
1. Go to your channel: https://t.me/angizeshivahid
2. Find the message IDs you want to forward daily
3. Add them to the list below
    """)
    
    # You can add your channel message IDs here
    # These are the messages that will be forwarded to users
    message_ids = [
        248,  # Add your actual message IDs here
        249,
        250,
        251,
        252,
        # Add more message IDs as needed
    ]
    
    print(f"📝 Adding message IDs: {message_ids}")
    
    # Add messages to tracker
    added_count = tracker.add_multiple_messages(message_ids, channel_id)
    
    print(f"✅ Successfully added {added_count}/{len(message_ids)} messages")
    
    # Show current stats
    stats = tracker.get_stats()
    print(f"\n📊 Current Stats:")
    print(f"   Total messages: {stats.get('total_messages', 0)}")
    print(f"   Unused messages: {stats.get('unused_messages', 0)}")
    print(f"   Used messages: {stats.get('used_messages', 0)}")
    
    # Show next message to be forwarded
    next_message = tracker.get_unused_message()
    if next_message:
        message_id, channel = next_message
        print(f"\n📤 Next message to forward: {message_id} from {channel}")
    else:
        print(f"\n⚠️ No unused messages available!")
    
    print(f"""
🎯 Setup Complete!

Your bot will now forward messages from the channel in order:
- Each day at 8:00 AM, the bot will take the next unused message
- Forward it from your channel to all database users
- Mark the message as used

To add more messages later, edit this script and run it again.
    """)

async def interactive_setup():
    """Interactive setup for adding messages"""
    
    channel_id = os.getenv('CHANNEL_ID', '@angizeshivahid')
    tracker = ChannelMessageTracker()
    
    print(f"""
🔧 Interactive Channel Message Setup

Channel: {channel_id}
    """)
    
    while True:
        try:
            print("\nOptions:")
            print("1. Add single message ID")
            print("2. Add multiple message IDs")
            print("3. View current messages")
            print("4. Reset all messages to unused")
            print("5. Exit")
            
            choice = input("\nChoose option (1-5): ").strip()
            
            if choice == "1":
                message_id = input("Enter message ID: ").strip()
                try:
                    message_id = int(message_id)
                    if tracker.add_channel_message(message_id, channel_id):
                        print(f"✅ Added message {message_id}")
                    else:
                        print(f"❌ Failed to add message {message_id}")
                except ValueError:
                    print("❌ Invalid message ID!")
            
            elif choice == "2":
                ids_input = input("Enter message IDs (comma separated): ").strip()
                try:
                    message_ids = [int(x.strip()) for x in ids_input.split(',')]
                    added = tracker.add_multiple_messages(message_ids, channel_id)
                    print(f"✅ Added {added}/{len(message_ids)} messages")
                except ValueError:
                    print("❌ Invalid message IDs!")
            
            elif choice == "3":
                messages = tracker.get_all_messages()
                if messages:
                    print(f"\n📋 All Messages:")
                    print(f"{'ID':<6} {'Channel':<15} {'Used':<6} {'Forwards':<9} {'Date'}")
                    print("-" * 60)
                    for msg in messages:
                        msg_id, channel, date_posted, forwards, last_fwd, is_used, created = msg
                        used_str = "✅" if is_used else "⏳"
                        print(f"{msg_id:<6} {channel:<15} {used_str:<6} {forwards:<9} {date_posted or 'N/A'}")
                else:
                    print("📭 No messages found")
                
                # Show stats
                stats = tracker.get_stats()
                print(f"\n📊 Stats: {stats.get('total_messages', 0)} total, {stats.get('unused_messages', 0)} unused")
            
            elif choice == "4":
                confirm = input("Reset all messages to unused? (y/N): ").strip().lower()
                if confirm == 'y':
                    if tracker.reset_all_messages():
                        print("✅ All messages reset to unused")
                    else:
                        print("❌ Failed to reset messages")
            
            elif choice == "5":
                print("👋 Goodbye!")
                break
            
            else:
                print("❌ Invalid choice!")
                
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"❌ Error: {e}")

if __name__ == "__main__":
    print("🚀 Channel Message Setup")
    print("=" * 40)
    
    mode = input("Choose mode:\n1. Auto setup (recommended)\n2. Interactive setup\nChoice (1/2): ").strip()
    
    if mode == "1":
        asyncio.run(setup_messages())
    elif mode == "2":
        asyncio.run(interactive_setup())
    else:
        print("❌ Invalid choice!")
