#!/usr/bin/env python3
"""
Send motivational messages to Telegram channel
"""

import os
import sys
import asyncio
import logging
from datetime import datetime
from dotenv import load_dotenv
from telegram import Bot

from database import DatabaseManager

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)

logger = logging.getLogger(__name__)

async def send_to_channel():
    """Send motivational message to channel"""
    
    bot_token = os.getenv('BOT_TOKEN')
    channel_id = os.getenv('CHANNEL_ID')
    
    if not bot_token:
        logger.error("BOT_TOKEN not found!")
        return False
    
    if not channel_id:
        logger.error("CHANNEL_ID not found!")
        return False
    
    logger.info(f"📤 Sending message to channel: {channel_id}")
    
    # Initialize components
    db_manager = DatabaseManager()
    bot = Bot(token=bot_token)
    
    # Get random content
    content = db_manager.get_random_content()
    if not content:
        logger.error("❌ No content found in database!")
        return False
    
    content_id, content_type, text, file_path, file_id = content
    logger.info(f"📝 Sending content: {text[:50]}...")
    
    # Create beautiful channel message
    current_time = datetime.now()
    time_str = current_time.strftime("%H:%M")
    date_str = current_time.strftime("%Y/%m/%d")
    day_name = current_time.strftime("%A")
    
    # Persian day names
    persian_days = {
        'Monday': 'دوشنبه',
        'Tuesday': 'سه‌شنبه', 
        'Wednesday': 'چهارشنبه',
        'Thursday': 'پنج‌شنبه',
        'Friday': 'جمعه',
        'Saturday': 'شنبه',
        'Sunday': 'یکشنبه'
    }
    
    persian_day = persian_days.get(day_name, day_name)
    
    # Beautiful message format for channel
    message_text = f"""🌟 پیام انگیزشی روزانه

{text}

━━━━━━━━━━━━━━━━━━━━
📅 {persian_day} | {date_str}
🕐 {time_str}
💫 کانال انگیزشی واحد

#انگیزشی #موفقیت #روزانه #پیام_روز"""

    try:
        # Send based on content type
        if content_type == 'text':
            message = await bot.send_message(
                chat_id=channel_id,
                text=message_text,
                parse_mode='HTML'
            )
        elif content_type == 'photo' and file_path:
            message = await bot.send_photo(
                chat_id=channel_id,
                photo=file_path,
                caption=message_text,
                parse_mode='HTML'
            )
        elif content_type == 'video' and file_path:
            message = await bot.send_video(
                chat_id=channel_id,
                video=file_path,
                caption=message_text,
                parse_mode='HTML'
            )
        elif content_type == 'audio' and file_path:
            message = await bot.send_audio(
                chat_id=channel_id,
                audio=file_path,
                caption=message_text,
                parse_mode='HTML'
            )
        else:
            # Fallback to text
            message = await bot.send_message(
                chat_id=channel_id,
                text=message_text,
                parse_mode='HTML'
            )
        
        logger.info(f"✅ Message sent to channel successfully!")
        logger.info(f"📱 Message ID: {message.message_id}")
        logger.info(f"📅 Sent at: {message.date}")
        logger.info(f"🔗 Channel: {channel_id}")
        
        # Get channel info
        try:
            chat_info = await bot.get_chat(channel_id)
            logger.info(f"📋 Channel: {chat_info.title}")
            logger.info(f"👥 Members: {getattr(chat_info, 'member_count', 'Unknown')}")
        except Exception as e:
            logger.warning(f"Could not get channel info: {e}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Failed to send to channel: {e}")
        
        # Common error solutions
        if "chat not found" in str(e).lower():
            logger.error("💡 Solution: Make sure the channel username is correct (@angizeshivahid)")
        elif "not enough rights" in str(e).lower():
            logger.error("💡 Solution: Add the bot as admin to the channel with 'Post Messages' permission")
        elif "bot was blocked" in str(e).lower():
            logger.error("💡 Solution: Unblock the bot or check bot permissions")
        
        return False

async def test_channel_access():
    """Test if bot can access the channel"""
    
    bot_token = os.getenv('BOT_TOKEN')
    channel_id = os.getenv('CHANNEL_ID')
    
    if not bot_token or not channel_id:
        print("❌ BOT_TOKEN or CHANNEL_ID not found in .env file!")
        return
    
    bot = Bot(token=bot_token)
    
    try:
        # Test channel access
        chat_info = await bot.get_chat(channel_id)
        
        description = getattr(chat_info, 'description', 'No description')
        if description and len(description) > 100:
            description = description[:100] + "..."

        print(f"""
✅ Channel Access Test Successful!

📋 Channel Information:
━━━━━━━━━━━━━━━━━━━━
🏷 Title: {chat_info.title}
🆔 ID: {chat_info.id}
👤 Username: @{chat_info.username if chat_info.username else 'No username'}
📝 Type: {chat_info.type}
📝 Description: {description}

🤖 Bot can access this channel!
        """)
        
        return True
        
    except Exception as e:
        print(f"""
❌ Channel Access Test Failed!

Error: {e}

💡 Common Solutions:
1. Make sure channel username is correct: @angizeshivahid
2. Add bot as admin to the channel
3. Give bot 'Post Messages' permission
4. Make sure channel exists and is accessible
        """)
        return False

if __name__ == "__main__":
    print("""
📺 Channel Message Sender

This script will send a motivational message to your Telegram channel.
Channel: @angizeshivahid

Choose an option:
1. Test channel access
2. Send message to channel
    """)
    
    import sys
    if len(sys.argv) > 1 and sys.argv[1] == "test":
        asyncio.run(test_channel_access())
    else:
        asyncio.run(send_to_channel())
