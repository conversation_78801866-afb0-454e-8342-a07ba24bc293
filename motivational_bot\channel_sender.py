#!/usr/bin/env python3
"""
Channel-based message sender
Sends motivational messages through a Telegram channel
"""

import os
import sys
import asyncio
import logging
from datetime import datetime
from dotenv import load_dotenv
from telegram import Bo<PERSON>

from database import DatabaseManager

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)

logger = logging.getLogger(__name__)

class ChannelSender:
    def __init__(self, bot_token: str, channel_id: str):
        self.bot = Bot(token=bot_token)
        self.channel_id = channel_id
        self.db = DatabaseManager()
    
    async def send_to_channel(self, content_id=None):
        """Send motivational message to channel"""
        try:
            # Get content from database
            if content_id:
                content = self.db.get_content_by_id(content_id)
            else:
                content = self.db.get_random_content()
            
            if not content:
                logger.error("No content found to send")
                return False
            
            content_id, content_type, text, file_path, file_id = content
            
            # Create beautiful message format
            current_time = datetime.now().strftime("%Y/%m/%d - %H:%M")
            
            message_text = f"""🌟 پیام انگیزشی روزانه

{text}

━━━━━━━━━━━━━━━━━━━━
📅 {current_time}
💫 کانال انگیزشی | @YourChannelUsername

#انگیزشی #موفقیت #روزانه"""

            # Send based on content type
            if content_type == 'text':
                message = await self.bot.send_message(
                    chat_id=self.channel_id,
                    text=message_text,
                    parse_mode='HTML'
                )
            elif content_type == 'photo' and file_path:
                message = await self.bot.send_photo(
                    chat_id=self.channel_id,
                    photo=file_path,
                    caption=message_text,
                    parse_mode='HTML'
                )
            elif content_type == 'video' and file_path:
                message = await self.bot.send_video(
                    chat_id=self.channel_id,
                    video=file_path,
                    caption=message_text,
                    parse_mode='HTML'
                )
            elif content_type == 'audio' and file_path:
                message = await self.bot.send_audio(
                    chat_id=self.channel_id,
                    audio=file_path,
                    caption=message_text,
                    parse_mode='HTML'
                )
            else:
                # Fallback to text
                message = await self.bot.send_message(
                    chat_id=self.channel_id,
                    text=message_text,
                    parse_mode='HTML'
                )
            
            logger.info(f"✅ Message sent to channel successfully!")
            logger.info(f"📱 Message ID: {message.message_id}")
            logger.info(f"📅 Sent at: {message.date}")
            
            # Now forward to subscribers
            await self.forward_to_subscribers(message.message_id)
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to send to channel: {e}")
            return False
    
    async def forward_to_subscribers(self, message_id: int):
        """Forward channel message to all subscribers"""
        try:
            active_users = self.db.get_active_users()
            if not active_users:
                logger.info("No active users to forward to")
                return
            
            success_count = 0
            for chat_id in active_users:
                try:
                    # Forward the channel message to user
                    await self.bot.forward_message(
                        chat_id=chat_id,
                        from_chat_id=self.channel_id,
                        message_id=message_id
                    )
                    success_count += 1
                    logger.info(f"Message forwarded to user {chat_id}")
                    
                    # Small delay to avoid rate limiting
                    await asyncio.sleep(0.1)
                    
                except Exception as e:
                    logger.error(f"Failed to forward to user {chat_id}: {e}")
            
            logger.info(f"Message forwarded to {success_count}/{len(active_users)} users")
            
        except Exception as e:
            logger.error(f"Error forwarding to subscribers: {e}")
    
    async def send_direct_to_users(self):
        """Send message directly to users (alternative method)"""
        try:
            active_users = self.db.get_active_users()
            if not active_users:
                logger.info("No active users to send to")
                return
            
            content = self.db.get_random_content()
            if not content:
                logger.error("No content found")
                return
            
            content_id, content_type, text, file_path, file_id = content
            
            # Create personal message format
            current_time = datetime.now().strftime("%H:%M")
            
            message_text = f"""💌 پیام انگیزشی شما

{text}

🕐 {current_time} | 🌅 صبح بخیر!
💫 موفق باشید"""

            success_count = 0
            for chat_id in active_users:
                try:
                    if content_type == 'text':
                        await self.bot.send_message(
                            chat_id=chat_id,
                            text=message_text,
                            parse_mode='HTML',
                            disable_notification=False
                        )
                    elif content_type == 'photo' and file_path:
                        await self.bot.send_photo(
                            chat_id=chat_id,
                            photo=file_path,
                            caption=message_text,
                            parse_mode='HTML'
                        )
                    # Add other content types as needed
                    else:
                        await self.bot.send_message(
                            chat_id=chat_id,
                            text=message_text,
                            parse_mode='HTML'
                        )
                    
                    success_count += 1
                    await asyncio.sleep(0.1)  # Rate limiting
                    
                except Exception as e:
                    logger.error(f"Failed to send to user {chat_id}: {e}")
            
            logger.info(f"Direct messages sent to {success_count}/{len(active_users)} users")
            
        except Exception as e:
            logger.error(f"Error in send_direct_to_users: {e}")

async def main():
    """Test the channel sender"""
    bot_token = os.getenv('BOT_TOKEN')
    
    # You need to replace this with your actual channel ID
    # Format: @channelname or -100xxxxxxxxx
    channel_id = "@your_channel_name"  # Replace with your channel
    
    if not bot_token:
        logger.error("BOT_TOKEN not found!")
        return
    
    print(f"""
🚀 Channel Sender Test

Bot Token: {bot_token[:20]}...
Channel ID: {channel_id}

Choose an option:
1. Send to channel and forward to users
2. Send directly to users (no channel)
    """)
    
    sender = ChannelSender(bot_token, channel_id)
    
    # Option 1: Send to channel (you need to set up channel first)
    # await sender.send_to_channel()
    
    # Option 2: Send directly to users
    await sender.send_direct_to_users()

if __name__ == "__main__":
    asyncio.run(main())
