#!/usr/bin/env python3
"""
Forward messages from channel to database users
<PERSON><PERSON> reads messages from channel and forwards them to subscribers
"""

import os
import sys
import asyncio
import logging
from datetime import datetime, timedelta
from dotenv import load_dotenv
from telegram import Bot

from database import DatabaseManager

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)

logger = logging.getLogger(__name__)

class ChannelForwarder:
    def __init__(self, bot_token: str, channel_id: str):
        self.bot = Bot(token=bot_token)
        self.channel_id = channel_id
        self.db = DatabaseManager()
    
    async def get_latest_channel_message(self):
        """Get the latest message from channel"""
        try:
            # Get channel info first
            chat = await self.bot.get_chat(self.channel_id)
            logger.info(f"📺 Channel: {chat.title} (@{chat.username})")
            
            # Get recent messages by trying different message IDs
            # Start from a recent message ID and work backwards
            latest_message = None
            
            # Try to get the latest message by checking recent IDs
            for i in range(10):  # Check last 10 messages
                try:
                    # We'll start from a high number and work down
                    # You might need to adjust this based on your channel
                    message_id = 250 - i  # Start from message 250 and go down
                    
                    # Try to get the message
                    message = await self.bot.forward_message(
                        chat_id=self.bot.id,  # Forward to bot itself (won't work but we can catch the error)
                        from_chat_id=self.channel_id,
                        message_id=message_id
                    )
                    
                except Exception as e:
                    if "message not found" not in str(e).lower():
                        # This means the message exists, we just can't forward to bot
                        latest_message = message_id
                        break
            
            return latest_message
            
        except Exception as e:
            logger.error(f"Error getting latest channel message: {e}")
            return None
    
    async def forward_channel_message(self, message_id: int):
        """Forward a specific message from channel to all database users"""
        try:
            # Get active users
            active_users = self.db.get_active_users()
            if not active_users:
                logger.warning("No active users to forward to")
                return False
            
            logger.info(f"📤 Forwarding message {message_id} from {self.channel_id} to {len(active_users)} users")
            
            success_count = 0
            failed_count = 0
            
            for user_id in active_users:
                try:
                    # Forward the channel message to user
                    forwarded_message = await self.bot.forward_message(
                        chat_id=user_id,
                        from_chat_id=self.channel_id,
                        message_id=message_id
                    )
                    
                    success_count += 1
                    logger.info(f"✅ Message {message_id} forwarded to user {user_id}")
                    
                    # Small delay to avoid rate limiting
                    await asyncio.sleep(0.1)
                    
                except Exception as e:
                    failed_count += 1
                    logger.error(f"❌ Failed to forward to user {user_id}: {e}")
                    
                    # Handle specific errors
                    if "bot was blocked" in str(e).lower():
                        logger.info(f"🚫 User {user_id} blocked the bot - deactivating")
                        self.db.deactivate_user(user_id)
                    elif "chat not found" in str(e).lower():
                        logger.info(f"👻 User {user_id} not found - deactivating")
                        self.db.deactivate_user(user_id)
                    elif "message not found" in str(e).lower():
                        logger.error(f"📭 Message {message_id} not found in channel")
                        return False
            
            logger.info(f"🎯 Forwarding completed: {success_count} success, {failed_count} failed")
            return success_count > 0
            
        except Exception as e:
            logger.error(f"Error in forward_channel_message: {e}")
            return False
    
    async def forward_latest_message(self):
        """Forward the latest message from channel"""
        try:
            # For now, we'll use a known message ID
            # You can manually specify which message to forward
            
            # Get the latest message ID (you might need to adjust this)
            latest_message_id = 250  # Replace with actual latest message ID
            
            logger.info(f"🔍 Attempting to forward message ID: {latest_message_id}")
            
            result = await self.forward_channel_message(latest_message_id)
            
            if result:
                logger.info(f"✅ Successfully forwarded latest message")
                return True
            else:
                logger.error(f"❌ Failed to forward latest message")
                return False
                
        except Exception as e:
            logger.error(f"Error forwarding latest message: {e}")
            return False
    
    async def forward_specific_message(self, message_id: int):
        """Forward a specific message ID from channel"""
        logger.info(f"📤 Forwarding specific message ID: {message_id}")
        return await self.forward_channel_message(message_id)
    
    async def list_recent_messages(self):
        """List recent messages in channel (for debugging)"""
        try:
            logger.info(f"🔍 Checking recent messages in {self.channel_id}")
            
            # Try different message IDs to see what exists
            existing_messages = []
            
            for message_id in range(245, 255):  # Check messages 245-254
                try:
                    # Try to forward to a non-existent chat to test if message exists
                    await self.bot.forward_message(
                        chat_id=999999999,  # Non-existent chat
                        from_chat_id=self.channel_id,
                        message_id=message_id
                    )
                except Exception as e:
                    if "chat not found" in str(e).lower():
                        # Message exists but chat doesn't - this is what we want
                        existing_messages.append(message_id)
                    elif "message not found" not in str(e).lower():
                        # Some other error, message might exist
                        existing_messages.append(message_id)
            
            logger.info(f"📋 Found potential message IDs: {existing_messages}")
            return existing_messages
            
        except Exception as e:
            logger.error(f"Error listing recent messages: {e}")
            return []

async def main():
    """Main function"""
    bot_token = os.getenv('BOT_TOKEN')
    channel_id = os.getenv('CHANNEL_ID')
    
    if not bot_token or not channel_id:
        logger.error("BOT_TOKEN or CHANNEL_ID not found!")
        return
    
    forwarder = ChannelForwarder(bot_token, channel_id)
    
    print(f"""
📺 Channel Message Forwarder

This script forwards messages from your channel to database users.

Channel: {channel_id}
Bot Token: {bot_token[:20]}...

Options:
1. Forward latest message
2. Forward specific message ID
3. List recent messages
    """)
    
    try:
        choice = input("Choose option (1/2/3): ").strip()
        
        if choice == "1":
            logger.info("🚀 Forwarding latest message...")
            await forwarder.forward_latest_message()
            
        elif choice == "2":
            message_id = input("Enter message ID to forward: ").strip()
            try:
                message_id = int(message_id)
                logger.info(f"🚀 Forwarding message ID {message_id}...")
                await forwarder.forward_specific_message(message_id)
            except ValueError:
                logger.error("Invalid message ID!")
                
        elif choice == "3":
            logger.info("🔍 Listing recent messages...")
            await forwarder.list_recent_messages()
            
        else:
            logger.error("Invalid choice!")
            
    except KeyboardInterrupt:
        logger.info("Operation cancelled by user")
    except Exception as e:
        logger.error(f"Error in main: {e}")

if __name__ == "__main__":
    asyncio.run(main())
