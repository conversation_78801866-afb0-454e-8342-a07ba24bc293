#!/usr/bin/env python3
"""
Send a personal message directly to user's private chat
This will create a new conversation, not through the bot interface
"""

import os
import sys
import asyncio
import logging
from dotenv import load_dotenv
from telegram import Bo<PERSON>

from database import DatabaseManager

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)

logger = logging.getLogger(__name__)

async def send_personal_message():
    """Send personal message to user's private chat"""
    
    bot_token = os.getenv('BOT_TOKEN')
    if not bot_token:
        logger.error("BOT_TOKEN not found!")
        return
    
    # Your personal chat ID (this should be your personal Telegram user ID)
    # To get your personal chat ID, send a message to @userinfobot
    target_user_id = 222030871  # Your personal Telegram user ID
    
    logger.info(f"📤 Sending personal message to user ID: {target_user_id}")
    
    # Initialize components
    db_manager = DatabaseManager()
    bot = Bot(token=bot_token)
    
    # Get random content
    content = db_manager.get_random_content()
    if not content:
        logger.error("❌ No content found in database!")
        return
    
    content_id, content_type, text, file_path, file_id = content
    logger.info(f"📝 Sending content: {text[:50]}...")
    
    try:
        # Send message to user's personal chat (not bot chat)
        # This will appear as a new message from the bot in their personal messages
        
        message_text = f"""🌟 پیام انگیزشی روزانه

{text}

💫 این پیام از سیستم انگیزشی شما ارسال شده است.
🕐 زمان ارسال: {asyncio.get_event_loop().time()}

موفق باشید! 🚀"""

        if content_type == 'text':
            message = await bot.send_message(
                chat_id=target_user_id,
                text=message_text,
                parse_mode='HTML',
                disable_notification=False  # This ensures the user gets notified
            )
        elif content_type == 'photo' and file_path:
            message = await bot.send_photo(
                chat_id=target_user_id,
                photo=file_path,
                caption=message_text,
                disable_notification=False
            )
        elif content_type == 'video' and file_path:
            message = await bot.send_video(
                chat_id=target_user_id,
                video=file_path,
                caption=message_text,
                disable_notification=False
            )
        elif content_type == 'audio' and file_path:
            message = await bot.send_audio(
                chat_id=target_user_id,
                audio=file_path,
                caption=message_text,
                disable_notification=False
            )
        else:
            # Fallback to text
            message = await bot.send_message(
                chat_id=target_user_id,
                text=message_text,
                parse_mode='HTML',
                disable_notification=False
            )
        
        logger.info(f"✅ Personal message sent successfully!")
        logger.info(f"📱 Message ID: {message.message_id}")
        logger.info(f"📅 Sent at: {message.date}")
        logger.info(f"💬 Chat type: {message.chat.type}")
        
        # Also send a follow-up message to explain
        await asyncio.sleep(2)
        follow_up = await bot.send_message(
            chat_id=target_user_id,
            text="ℹ️ این پیام از ربات انگیزشی شما ارسال شده است. هر روز ساعت 8 صبح پیام جدیدی دریافت خواهید کرد.",
            disable_notification=True  # Silent notification for this one
        )
        
        logger.info(f"✅ Follow-up message sent: {follow_up.message_id}")
        
    except Exception as e:
        logger.error(f"❌ Failed to send personal message: {e}")
        logger.error(f"Error details: {type(e).__name__}: {str(e)}")

if __name__ == "__main__":
    print("""
📤 Sending Personal Message

This script will send a motivational message directly to your personal Telegram chat.
The message will appear as a new conversation with the bot.

Target User ID: 222030871

Note: Make sure you have started a conversation with the bot first!
    """)
    
    asyncio.run(send_personal_message())
