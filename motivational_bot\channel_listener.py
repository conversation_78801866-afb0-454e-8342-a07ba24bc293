#!/usr/bin/env python3
"""
Channel Message Listener
Listens for new messages in the channel and stores them
"""

import os
import asyncio
import logging
from dotenv import load_dotenv
from telegram import Update
from telegram.ext import Application, MessageHandler, filters, ContextTypes

from database import DatabaseManager
from channel_message_tracker import ChannelMessageTracker

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)

logger = logging.getLogger(__name__)

class ChannelListener:
    def __init__(self, bot_token: str, channel_id: str):
        self.bot_token = bot_token
        self.channel_id = channel_id
        self.db = DatabaseManager()
        self.tracker = ChannelMessageTracker()
        self.stored_messages = {}  # Store message content in memory
    
    async def handle_channel_post(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle new channel posts"""
        try:
            if not update.channel_post:
                return
            
            message = update.channel_post
            channel_username = f"@{message.chat.username}" if message.chat.username else str(message.chat.id)
            
            # Check if this is our target channel
            if channel_username != self.channel_id:
                return
            
            message_id = message.message_id
            logger.info(f"📨 New channel message received: {message_id}")
            
            # Extract message content
            content = {
                'message_id': message_id,
                'text': message.text or message.caption or "پیام حاوی رسانه",
                'type': 'text' if message.text else 'media',
                'has_media': bool(message.photo or message.video or message.document or message.audio),
                'media_type': self._get_media_type(message),
                'date': message.date.isoformat() if message.date else None,
                'channel_id': channel_username
            }
            
            # Store in memory
            self.stored_messages[message_id] = content
            
            # Add to tracker
            self.tracker.add_message(message_id, channel_username)
            
            logger.info(f"✅ Stored message {message_id}: {content['text'][:50]}...")
            
            print(f"""
📨 New Message Captured!
🆔 ID: {message_id}
📝 Text: {content['text'][:100]}...
📅 Date: {content['date']}
🎯 Type: {content['type']}
📺 Channel: {channel_username}
            """)
            
        except Exception as e:
            logger.error(f"Error handling channel post: {e}")
    
    def _get_media_type(self, message):
        """Determine the type of media in message"""
        if message.photo:
            return 'photo'
        elif message.video:
            return 'video'
        elif message.document:
            return 'document'
        elif message.audio:
            return 'audio'
        elif message.voice:
            return 'voice'
        elif message.video_note:
            return 'video_note'
        elif message.sticker:
            return 'sticker'
        else:
            return None
    
    def get_stored_message(self, message_id: int):
        """Get stored message content"""
        return self.stored_messages.get(message_id)
    
    def get_all_stored_messages(self):
        """Get all stored messages"""
        return self.stored_messages
    
    async def start_listening(self):
        """Start listening for channel messages"""
        try:
            # Create application
            application = Application.builder().token(self.bot_token).build()
            
            # Add handler for channel posts
            application.add_handler(
                MessageHandler(filters.UpdateType.CHANNEL_POST, self.handle_channel_post)
            )
            
            print(f"""
🎧 Channel Listener Started

📺 Listening to: {self.channel_id}
🤖 Bot is admin: ✅
📨 Waiting for new messages...

💡 Post a new message in your channel to test!
Press Ctrl+C to stop.
            """)
            
            # Start polling
            await application.run_polling(drop_pending_updates=True)
            
        except KeyboardInterrupt:
            print("\n👋 Stopping listener...")
        except Exception as e:
            logger.error(f"Error in listener: {e}")

async def main():
    """Main function"""
    bot_token = os.getenv('BOT_TOKEN')
    channel_id = os.getenv('CHANNEL_ID')
    
    if not bot_token or not channel_id:
        logger.error("BOT_TOKEN or CHANNEL_ID not found!")
        return
    
    listener = ChannelListener(bot_token, channel_id)
    
    print(f"""
🎧 Channel Message Listener

This will:
1. 👂 Listen for new messages in your channel
2. 📝 Capture the actual message content
3. 💾 Store messages for later use
4. 🔄 Add to message tracker

Channel: {channel_id}

Ready to capture real channel messages!
    """)
    
    try:
        await listener.start_listening()
    except Exception as e:
        logger.error(f"Error in main: {e}")

if __name__ == "__main__":
    asyncio.run(main())
