import sqlite3
import logging
from datetime import datetime
from typing import List, Optional, Tuple

class DatabaseManager:
    def __init__(self, db_path: str = "motivational_bot.db"):
        self.db_path = db_path
        self.init_database()
    
    def init_database(self):
        """Initialize database tables"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # Users table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS users (
                        id INTEGER PRIMARY KEY,
                        chat_id INTEGER UNIQUE NOT NULL,
                        username TEXT,
                        first_name TEXT,
                        last_name TEXT,
                        is_active BOOLEAN DEFAULT 1,
                        joined_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        last_message_date TIMESTAMP
                    )
                ''')
                
                # Content table for motivational content
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS content (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        content_type TEXT NOT NULL, -- 'text', 'photo', 'video', 'audio'
                        content_text TEXT,
                        file_path TEXT,
                        file_id TEXT, -- Telegram file_id for media
                        is_active BOOLEAN DEFAULT 1,
                        created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        usage_count INTEGER DEFAULT 0,
                        last_used TIMESTAMP
                    )
                ''')
                
                # Settings table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS settings (
                        key TEXT PRIMARY KEY,
                        value TEXT NOT NULL,
                        updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                ''')
                
                # Insert default settings
                cursor.execute('''
                    INSERT OR IGNORE INTO settings (key, value) VALUES 
                    ('send_time', '08:00'),
                    ('timezone', 'Asia/Tehran'),
                    ('daily_message_enabled', '1')
                ''')
                
                conn.commit()
                logging.info("Database initialized successfully")
                
        except Exception as e:
            logging.error(f"Database initialization error: {e}")
    
    def add_user(self, chat_id: int, username: str = None, first_name: str = None, last_name: str = None) -> bool:
        """Add new user to database"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT OR REPLACE INTO users (chat_id, username, first_name, last_name, is_active)
                    VALUES (?, ?, ?, ?, 1)
                ''', (chat_id, username, first_name, last_name))
                conn.commit()
                return True
        except Exception as e:
            logging.error(f"Error adding user: {e}")
            return False
    
    def get_active_users(self) -> List[int]:
        """Get list of active user chat_ids"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('SELECT chat_id FROM users WHERE is_active = 1')
                return [row[0] for row in cursor.fetchall()]
        except Exception as e:
            logging.error(f"Error getting active users: {e}")
            return []
    
    def deactivate_user(self, chat_id: int) -> bool:
        """Deactivate user (unsubscribe)"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('UPDATE users SET is_active = 0 WHERE chat_id = ?', (chat_id,))
                conn.commit()
                return True
        except Exception as e:
            logging.error(f"Error deactivating user: {e}")
            return False
    
    def add_content(self, content_type: str, content_text: str = None, file_path: str = None, file_id: str = None) -> bool:
        """Add motivational content"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT INTO content (content_type, content_text, file_path, file_id)
                    VALUES (?, ?, ?, ?)
                ''', (content_type, content_text, file_path, file_id))
                conn.commit()
                return True
        except Exception as e:
            logging.error(f"Error adding content: {e}")
            return False
    
    def get_random_content(self, content_type: str = None) -> Optional[Tuple]:
        """Get random content from database"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                if content_type:
                    cursor.execute('''
                        SELECT id, content_type, content_text, file_path, file_id 
                        FROM content 
                        WHERE content_type = ? AND is_active = 1 
                        ORDER BY RANDOM() LIMIT 1
                    ''', (content_type,))
                else:
                    cursor.execute('''
                        SELECT id, content_type, content_text, file_path, file_id 
                        FROM content 
                        WHERE is_active = 1 
                        ORDER BY RANDOM() LIMIT 1
                    ''')
                return cursor.fetchone()
        except Exception as e:
            logging.error(f"Error getting random content: {e}")
            return None
    
    def update_content_usage(self, content_id: int):
        """Update content usage statistics"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    UPDATE content 
                    SET usage_count = usage_count + 1, last_used = CURRENT_TIMESTAMP 
                    WHERE id = ?
                ''', (content_id,))
                conn.commit()
        except Exception as e:
            logging.error(f"Error updating content usage: {e}")
    
    def get_user_count(self) -> int:
        """Get total active users count"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('SELECT COUNT(*) FROM users WHERE is_active = 1')
                return cursor.fetchone()[0]
        except Exception as e:
            logging.error(f"Error getting user count: {e}")
            return 0
