#!/usr/bin/env python3
"""
Test specific message IDs to find which ones actually exist
"""

import os
import asyncio
import logging
from dotenv import load_dotenv
from telegram import Bo<PERSON>
from telegram.error import TelegramError

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_message_exists(bot, channel_id, message_id):
    """Test if a specific message exists"""
    try:
        # Try to copy message to test existence
        await bot.copy_message(
            chat_id=channel_id,
            from_chat_id=channel_id,
            message_id=message_id
        )
        return True, "Can copy"
        
    except TelegramError as e:
        error_msg = str(e).lower()
        if "message to copy not found" in error_msg:
            return False, "Not found"
        elif "can't copy message to the same chat" in error_msg:
            return True, "Exists (can't copy to same)"
        else:
            return True, f"Exists ({e})"

async def main():
    bot_token = os.getenv('BOT_TOKEN')
    channel_id = os.getenv('CHANNEL_ID')
    
    if not bot_token or not channel_id:
        print("❌ BOT_TOKEN or CHANNEL_ID not found!")
        return
    
    bot = Bot(token=bot_token)
    
    print(f"""
🔍 Testing Message Existence

Channel: {channel_id}
Testing various message IDs to find existing ones...
    """)
    
    # Test some recent message IDs
    test_ids = [
        # Recent IDs that might exist
        2000, 1999, 1998, 1997, 1996, 1995,
        # Some older IDs
        1000, 900, 800, 700, 600, 500,
        # Even older
        400, 300, 250, 200, 150, 100, 50, 25, 10, 5, 1
    ]
    
    existing_messages = []
    
    print("🚀 Testing message IDs...")
    
    for msg_id in test_ids:
        try:
            exists, reason = await test_message_exists(bot, channel_id, msg_id)
            
            if exists:
                existing_messages.append(msg_id)
                print(f"✅ Message {msg_id}: EXISTS ({reason})")
            else:
                print(f"❌ Message {msg_id}: {reason}")
                
            await asyncio.sleep(0.2)  # Rate limiting
            
        except Exception as e:
            print(f"⚠️ Message {msg_id}: Error - {e}")
    
    print(f"""
📋 Results Summary:
   🔍 Tested: {len(test_ids)} message IDs
   ✅ Found: {len(existing_messages)} existing messages
   📝 Existing IDs: {existing_messages}
    """)
    
    if existing_messages:
        # Test the first existing message with advanced reader
        test_id = existing_messages[0]
        print(f"\n🧪 Testing advanced reading of message {test_id}...")
        
        from advanced_channel_reader import AdvancedChannelReader
        reader = AdvancedChannelReader(bot_token, channel_id)
        
        content = await reader.read_channel_message_advanced(test_id)
        if content:
            print(f"""
📖 Advanced Reading Result:
   🆔 ID: {content.get('message_id')}
   📝 Type: {content.get('type')}
   ✅ Exists: {content.get('exists')}
   📄 Text: {content.get('text', 'No text')[:200]}...
            """)
        else:
            print("❌ Advanced reading failed")
    
    else:
        print("⚠️ No existing messages found in test range")

if __name__ == "__main__":
    asyncio.run(main())
