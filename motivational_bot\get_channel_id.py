#!/usr/bin/env python3
"""
Get channel ID for your Telegram channel
"""

import os
import asyncio
import logging
from dotenv import load_dotenv
from telegram import Bot

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)

logger = logging.getLogger(__name__)

async def get_channel_info():
    """Get information about your channel"""
    
    bot_token = os.getenv('BOT_TOKEN')
    if not bot_token:
        logger.error("BOT_TOKEN not found!")
        return
    
    bot = Bot(token=bot_token)
    
    print("""
🔍 Channel ID Finder

To get your channel ID:
1. Create a channel in Telegram
2. Add your bot as admin to the channel
3. Send a test message to the channel
4. Forward that message to @userinfobot
5. The bot will show you the channel ID

Or you can use the channel username like: @your_channel_name

Example channel IDs:
- Public channel: @daily_motivation_fa
- Private channel: -1001234567890

Enter your channel username or ID below:
    """)
    
    channel_input = input("Channel username or ID: ").strip()
    
    if not channel_input:
        print("❌ No channel provided!")
        return
    
    try:
        # Try to get chat info
        chat = await bot.get_chat(chat_id=channel_input)
        
        print(f"""
✅ Channel Information:
━━━━━━━━━━━━━━━━━━━━
📋 Title: {chat.title}
🆔 ID: {chat.id}
👤 Username: @{chat.username if chat.username else 'No username'}
📝 Type: {chat.type}
👥 Members: {chat.member_count if hasattr(chat, 'member_count') else 'Unknown'}

💡 Use this ID in your .env file:
CHANNEL_ID={chat.id}
        """)
        
        # Test sending a message
        test_message = await bot.send_message(
            chat_id=channel_input,
            text="🧪 Test message from motivational bot!\n\nIf you see this, the bot can send messages to your channel! ✅"
        )
        
        print(f"✅ Test message sent successfully! Message ID: {test_message.message_id}")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        print("""
💡 Common issues:
1. Bot is not admin in the channel
2. Channel username is incorrect
3. Channel is private and bot doesn't have access

Solutions:
1. Add bot as admin to your channel
2. Make sure channel username is correct (@channelname)
3. For private channels, use the numeric ID (-1001234567890)
        """)

if __name__ == "__main__":
    asyncio.run(get_channel_info())
