#!/usr/bin/env python3
"""
Simple test for forwarding messages
"""

import os
import asyncio
import logging
from dotenv import load_dotenv
from telegram import <PERSON><PERSON>
from channel_message_tracker import ChannelMessageTracker
from database import DatabaseManager

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)

logger = logging.getLogger(__name__)

async def test_simple_forward():
    """Simple forward test"""
    
    bot_token = os.getenv('BOT_TOKEN')
    if not bot_token:
        logger.error("BOT_TOKEN not found!")
        return
    
    bot = Bot(token=bot_token)
    tracker = ChannelMessageTracker()
    db = DatabaseManager()
    
    print("""
🧪 Simple Forward Test
━━━━━━━━━━━━━━━━━━━━
    """)
    
    # Get next unused message
    unused_message = tracker.get_unused_message()
    if not unused_message:
        print("❌ No unused messages available")
        return
    
    message_id, channel_id = unused_message
    print(f"📤 Testing message {message_id} from {channel_id}")
    
    # Get active users
    active_users = db.get_active_users()
    print(f"👥 Active users: {len(active_users)}")
    
    if not active_users:
        print("❌ No active users found")
        return
    
    # Test forwarding to first user
    test_user = active_users[0]
    print(f"🎯 Testing forward to user: {test_user}")
    
    try:
        # Forward the message
        forwarded_message = await bot.forward_message(
            chat_id=test_user,
            from_chat_id=channel_id,
            message_id=message_id
        )
        
        print(f"✅ Message forwarded successfully!")
        print(f"📤 Forwarded message ID: {forwarded_message.message_id}")
        
        # Mark as used
        tracker.mark_message_used(message_id)
        print(f"✅ Message {message_id} marked as used")
        
    except Exception as e:
        print(f"❌ Error forwarding message: {e}")

if __name__ == "__main__":
    asyncio.run(test_simple_forward())
