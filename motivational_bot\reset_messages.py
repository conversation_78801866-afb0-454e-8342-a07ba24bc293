#!/usr/bin/env python3
"""
Reset message tracker to use only valid messages
"""

import os
import asyncio
import logging
from dotenv import load_dotenv
from channel_message_tracker import ChannelMessageTracker

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)

logger = logging.getLogger(__name__)

def reset_to_valid_messages():
    """Reset tracker to only use valid messages"""
    
    tracker = ChannelMessageTracker()
    
    print("""
🔄 Resetting Message Tracker
━━━━━━━━━━━━━━━━━━━━
This will remove all invalid messages and keep only working ones.
    """)
    
    # Get current stats
    stats = tracker.get_stats()
    print(f"📊 Current Stats:")
    print(f"   Total messages: {stats.get('total_messages', 0)}")
    print(f"   Unused messages: {stats.get('unused_messages', 0)}")
    print(f"   Used messages: {stats.get('used_messages', 0)}")
    
    # Clear all messages
    print("\n🗑️ Clearing all messages...")
    tracker.clear_all_messages()
    
    # Add only valid messages (100-110)
    valid_messages = [100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110]
    channel_id = "@angizeshivahid"
    
    print(f"\n✅ Adding valid messages: {valid_messages}")
    added = tracker.add_multiple_messages(valid_messages, channel_id)
    print(f"✅ Added {added}/{len(valid_messages)} valid messages")
    
    # Show new stats
    stats = tracker.get_stats()
    print(f"\n📊 New Stats:")
    print(f"   Total messages: {stats.get('total_messages', 0)}")
    print(f"   Unused messages: {stats.get('unused_messages', 0)}")
    print(f"   Used messages: {stats.get('used_messages', 0)}")
    
    # Show next message
    next_message = tracker.get_unused_message()
    if next_message:
        message_id, channel = next_message
        print(f"\n📤 Next message to forward: {message_id} from {channel}")
    else:
        print(f"\n⚠️ No unused messages available!")
    
    print(f"""
🎯 Reset Complete!
━━━━━━━━━━━━━━━━━━━━
Your bot will now only use valid messages that exist in the channel.
    """)

if __name__ == "__main__":
    reset_to_valid_messages()
