#!/usr/bin/env python3
"""
Motivational Bot - Daily Inspirational Messages
A Telegram bot that sends daily motivational content to subscribers
"""

import os
import sys
import asyncio
import logging
from dotenv import load_dotenv

from bot import MotivationalBot
from admin import AdminPanel
from database import DatabaseManager
from content_manager import ContentManager

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO,
    handlers=[
        logging.FileHandler('motivational_bot.log'),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

def main():
    """Main function to run the bot"""
    
    # Get configuration from environment
    bot_token = os.getenv('BOT_TOKEN')
    admin_chat_id = os.getenv('ADMIN_CHAT_ID')
    
    if not bot_token:
        logger.error("BOT_TOKEN not found in environment variables!")
        logger.error("Please create a .env file with your bot token:")
        logger.error("BOT_TOKEN=your_bot_token_here")
        sys.exit(1)
    
    # Parse admin chat IDs
    admin_chat_ids = []
    if admin_chat_id:
        try:
            admin_chat_ids = [int(id.strip()) for id in admin_chat_id.split(',')]
        except ValueError:
            logger.warning("Invalid ADMIN_CHAT_ID format. Admin features will be disabled.")
    
    try:
        # Initialize components
        logger.info("Initializing Motivational Bot...")
        
        db_manager = DatabaseManager()
        content_manager = ContentManager(db_manager)
        bot = MotivationalBot(bot_token)
        
        # Add admin panel if admin IDs are configured
        if admin_chat_ids:
            admin_panel = AdminPanel(db_manager, content_manager, admin_chat_ids)
            # Add admin handlers to bot
            for handler in admin_panel.get_handlers():
                bot.application.add_handler(handler)
            logger.info(f"Admin panel enabled for {len(admin_chat_ids)} admin(s)")
        
        # Run the bot
        logger.info("Starting Motivational Bot...")
        asyncio.run(bot.run())
        
    except KeyboardInterrupt:
        logger.info("Bot stopped by user")
    except Exception as e:
        logger.error(f"Fatal error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    print("""
🌟 Motivational Bot - Daily Inspirational Messages 🌟

This bot sends daily motivational content to subscribers at 8:00 AM.

Features:
- Daily motivational texts, images, videos, and audio
- User subscription management
- Admin panel for content management
- Automatic scheduling

Setup Instructions:
1. Create a .env file with your bot token:
   BOT_TOKEN=your_bot_token_here
   ADMIN_CHAT_ID=your_chat_id_here

2. Install dependencies:
   pip install -r requirements.txt

3. Run the bot:
   python main.py

For more information, check the README.md file.
    """)
    
    main()
