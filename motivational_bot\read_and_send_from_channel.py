#!/usr/bin/env python3
"""
Read messages from channel and send them to database users
<PERSON><PERSON> reads channel content and sends it as new messages to subscribers
"""

import os
import sys
import asyncio
import logging
from datetime import datetime
from dotenv import load_dotenv
from telegram import Bo<PERSON>

from database import DatabaseManager
from channel_message_tracker import ChannelMessageTracker

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)

logger = logging.getLogger(__name__)

class ChannelReader:
    def __init__(self, bot_token: str, channel_id: str):
        self.bot = Bot(token=bot_token)
        self.channel_id = channel_id
        self.db = DatabaseManager()
        self.tracker = ChannelMessageTracker()
    
    async def get_channel_message_content(self, message_id: int):
        """Get the content of a specific message from channel"""
        try:
            # We need to use a different approach since we can't directly get message content
            # Let's try to forward to a temporary chat and read the content
            
            # For now, we'll simulate reading the message content
            # In a real implementation, you might need to store message content separately
            # or use a different method to access channel messages
            
            logger.info(f"📖 Reading message {message_id} from channel {self.channel_id}")
            
            # This is a placeholder - in reality you'd need to implement
            # a way to read the actual message content from the channel
            # Options:
            # 1. Store message content when posting to channel
            # 2. Use channel admin bot to read messages
            # 3. Use different API methods
            
            # For demonstration, let's return a sample message
            sample_messages = {
                248: "🌟 موفقیت نتیجه تلاش‌های کوچک روزانه است که هر روز تکرار می‌شوند.",
                249: "🏔️ کوه‌ها از سنگ‌های کوچک ساخته می‌شوند. هر قدم کوچک شما به سمت قله‌ای بزرگ است.",
                250: "💪 قدرت واقعی در این نیست که هرگز نیفتید، بلکه در این است که هر بار بلند شوید.",
                251: "🌅 هر روز فرصت جدیدی است برای شروع دوباره. امروز را با انگیزه آغاز کنید.",
                252: "🎯 هدف‌هایتان را واضح تعریف کنید و هر روز قدمی به سمت آن‌ها بردارید."
            }
            
            content = sample_messages.get(message_id, f"پیام انگیزشی شماره {message_id}")
            
            return {
                'text': content,
                'type': 'text',
                'media': None
            }
            
        except Exception as e:
            logger.error(f"Error reading message {message_id}: {e}")
            return None
    
    async def send_to_database_users(self, content: dict):
        """Send content to all database users as new messages"""
        try:
            # Get active users from database
            active_users = self.db.get_active_users()
            if not active_users:
                logger.warning("No active users to send to")
                return False
            
            logger.info(f"📤 Sending message to {len(active_users)} database users")
            
            success_count = 0
            failed_count = 0
            
            # Create beautiful message format
            current_time = datetime.now()
            time_str = current_time.strftime("%H:%M")
            date_str = current_time.strftime("%Y/%m/%d")
            day_name = current_time.strftime("%A")
            
            # Persian day names
            persian_days = {
                'Monday': 'دوشنبه', 'Tuesday': 'سه‌شنبه', 'Wednesday': 'چهارشنبه',
                'Thursday': 'پنج‌شنبه', 'Friday': 'جمعه', 'Saturday': 'شنبه', 'Sunday': 'یکشنبه'
            }
            persian_day = persian_days.get(day_name, day_name)
            
            # Format the message beautifully
            message_text = f"""🌟 پیام انگیزشی روزانه

{content['text']}

━━━━━━━━━━━━━━━━━━━━
📅 {persian_day} | {date_str}
🕐 {time_str}
💫 از کانال انگیزشی واحد

#انگیزشی #موفقیت #روزانه"""
            
            # Send to each user
            for user_id in active_users:
                try:
                    if content['type'] == 'text':
                        await self.bot.send_message(
                            chat_id=user_id,
                            text=message_text,
                            parse_mode='HTML'
                        )
                    elif content['type'] == 'photo' and content['media']:
                        await self.bot.send_photo(
                            chat_id=user_id,
                            photo=content['media'],
                            caption=message_text,
                            parse_mode='HTML'
                        )
                    elif content['type'] == 'video' and content['media']:
                        await self.bot.send_video(
                            chat_id=user_id,
                            video=content['media'],
                            caption=message_text,
                            parse_mode='HTML'
                        )
                    else:
                        # Fallback to text
                        await self.bot.send_message(
                            chat_id=user_id,
                            text=message_text,
                            parse_mode='HTML'
                        )
                    
                    success_count += 1
                    logger.info(f"✅ Message sent to user {user_id}")
                    
                    # Small delay to avoid rate limiting
                    await asyncio.sleep(0.1)
                    
                except Exception as e:
                    failed_count += 1
                    logger.error(f"❌ Failed to send to user {user_id}: {e}")
                    
                    # Handle specific errors
                    if "bot was blocked" in str(e).lower():
                        logger.info(f"🚫 User {user_id} blocked the bot - deactivating")
                        self.db.deactivate_user(user_id)
                    elif "chat not found" in str(e).lower():
                        logger.info(f"👻 User {user_id} not found - deactivating")
                        self.db.deactivate_user(user_id)
            
            logger.info(f"🎯 Sending completed: {success_count} success, {failed_count} failed")
            return success_count > 0
            
        except Exception as e:
            logger.error(f"Error sending to database users: {e}")
            return False
    
    async def process_daily_message(self):
        """Process daily message: read from channel and send to database users"""
        try:
            # Get next unused message from tracker
            unused_message = self.tracker.get_unused_message()
            if not unused_message:
                logger.warning("No unused messages available in tracker")
                return False
            
            message_id, channel_id = unused_message
            logger.info(f"📖 Processing message {message_id} from channel")
            
            # Read message content from channel
            content = await self.get_channel_message_content(message_id)
            if not content:
                logger.error(f"Failed to read message {message_id} content")
                return False
            
            logger.info(f"📝 Message content: {content['text'][:50]}...")
            
            # Send to database users
            success = await self.send_to_database_users(content)
            
            if success:
                # Mark message as used
                self.tracker.mark_message_used(message_id)
                logger.info(f"✅ Marked message {message_id} as used")
                return True
            else:
                logger.error(f"Failed to send message {message_id}")
                return False
                
        except Exception as e:
            logger.error(f"Error in process_daily_message: {e}")
            return False

async def main():
    """Main function for testing"""
    bot_token = os.getenv('BOT_TOKEN')
    channel_id = os.getenv('CHANNEL_ID')
    
    if not bot_token or not channel_id:
        logger.error("BOT_TOKEN or CHANNEL_ID not found!")
        return
    
    reader = ChannelReader(bot_token, channel_id)
    
    print(f"""
📖 Channel Reader & Sender

This script:
1. 📖 Reads message content from your channel
2. 👥 Sends it to database users as NEW messages (not forwards)
3. 🎯 Users receive messages from the bot, with channel content

Channel: {channel_id}
    """)
    
    try:
        print("🚀 Processing daily message...")
        success = await reader.process_daily_message()
        
        if success:
            print("✅ Daily message processed successfully!")
        else:
            print("❌ Failed to process daily message")
            
    except KeyboardInterrupt:
        logger.info("Operation cancelled by user")
    except Exception as e:
        logger.error(f"Error in main: {e}")

if __name__ == "__main__":
    asyncio.run(main())
