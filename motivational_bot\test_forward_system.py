#!/usr/bin/env python3
"""
Test the new forward system
"""

import os
import asyncio
import logging
from dotenv import load_dotenv
from bot import MotivationalBot

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)

logger = logging.getLogger(__name__)

async def test_forward_system():
    """Test the forward system"""
    
    bot_token = os.getenv('BOT_TOKEN')
    if not bot_token:
        logger.error("BOT_TOKEN not found!")
        return
    
    print("""
🧪 Testing Forward System
━━━━━━━━━━━━━━━━━━━━
This will test forwarding a message from channel to database users.
    """)
    
    # Create bot instance
    bot = MotivationalBot(bot_token)

    # Initialize the application manually for testing
    from telegram.ext import Application
    bot.application = Application.builder().token(bot_token).build()
    await bot.application.initialize()

    try:
        # Test sending daily message (which now forwards from channel)
        print("📤 Testing daily message forwarding...")
        await bot.send_daily_message()
        print("✅ Forward test completed!")

    except Exception as e:
        print(f"❌ Error during forward test: {e}")
        logger.error(f"Forward test error: {e}")

    finally:
        # Cleanup
        if bot.application:
            await bot.application.shutdown()

if __name__ == "__main__":
    asyncio.run(test_forward_system())
