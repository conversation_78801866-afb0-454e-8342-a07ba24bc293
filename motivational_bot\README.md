# 🌟 ربات انگیزشی تلگرام

ربات تلگرامی که هر روز ساعت 8 صبح محتوای انگیزشی (متن، عکس، ویدئو، موسیقی) به کاربران عضو ارسال می‌کند.

## ✨ ویژگی‌ها

- 📅 ارسال خودکار پیام‌های انگیزشی هر روز ساعت 8 صبح
- 📝 پشتیبانی از انواع محتوا: متن، عکس، ویدئو، موسیقی
- 👥 سیستم عضویت و لغو عضویت آسان
- 🔧 پنل مدیریت برای ادمین‌ها
- 📊 آمار و گزارش‌گیری
- 💾 ذخیره‌سازی در دیتابیس SQLite

## 🚀 نصب و راه‌اندازی

### پیش‌نیازها

- Python 3.8 یا بالاتر
- حساب تلگرام
- ربات تلگرام (از @BotFather)

### مراحل نصب

1. **کلون کردن پروژه:**
```bash
git clone <repository-url>
cd motivational_bot
```

2. **نصب وابستگی‌ها:**
```bash
pip install -r requirements.txt
```

3. **تنظیم متغیرهای محیطی:**
```bash
cp .env.example .env
```

فایل `.env` را ویرایش کنید:
```
BOT_TOKEN=your_bot_token_here
ADMIN_CHAT_ID=your_chat_id_here
DATABASE_PATH=motivational_bot.db
TIMEZONE=Asia/Tehran
```

4. **اجرای ربات:**
```bash
python main.py
```

## 🤖 ساخت ربات تلگرام

1. به @BotFather در تلگرام پیام دهید
2. دستور `/newbot` را ارسال کنید
3. نام و username ربات را انتخاب کنید
4. توکن دریافتی را در فایل `.env` قرار دهید

## 📱 دستورات ربات

### دستورات کاربران:
- `/start` - شروع کار با ربات
- `/subscribe` - عضویت در پیام‌های روزانه
- `/unsubscribe` - لغو عضویت
- `/status` - بررسی وضعیت عضویت
- `/help` - راهنمای استفاده

### دستورات ادمین:
- `/admin` - ورود به پنل مدیریت

## 🔧 پنل مدیریت

پنل مدیریت امکانات زیر را فراهم می‌کند:

- 📊 مشاهده آمار کلی ربات
- 📝 مدیریت محتوای انگیزشی
- 👥 مدیریت کاربران
- 🔄 ارسال پیام تست

### افزودن محتوا:

**متن انگیزشی:**
- از پنل ادمین گزینه "افزودن متن" را انتخاب کنید

**رسانه (عکس، ویدئو، موسیقی):**
- فایل را مستقیماً به ربات ارسال کنید
- کپشن اختیاری اضافه کنید

## 📁 ساختار پروژه

```
motivational_bot/
├── main.py              # فایل اصلی اجرا
├── bot.py               # کلاس اصلی ربات
├── database.py          # مدیریت دیتابیس
├── content_manager.py   # مدیریت محتوا
├── scheduler.py         # زمان‌بندی ارسال پیام‌ها
├── admin.py             # پنل مدیریت
├── requirements.txt     # وابستگی‌ها
├── .env.example         # نمونه فایل تنظیمات
└── README.md           # راهنمای پروژه
```

## 🗄️ ساختار دیتابیس

### جدول users:
- ذخیره اطلاعات کاربران عضو
- وضعیت فعال/غیرفعال
- تاریخ عضویت

### جدول content:
- ذخیره محتوای انگیزشی
- انواع محتوا: text, photo, video, audio
- آمار استفاده

### جدول settings:
- تنظیمات ربات
- زمان ارسال
- منطقه زمانی

## ⏰ زمان‌بندی

ربات به صورت پیش‌فرض هر روز ساعت 8 صبح پیام ارسال می‌کند. برای تغییر زمان:

1. در فایل `scheduler.py` خط زیر را ویرایش کنید:
```python
schedule.every().day.at("08:00").do(self._schedule_daily_message)
```

2. یا از طریق دیتابیس تنظیمات را تغییر دهید.

## 🔒 امنیت

- توکن ربات را محرمانه نگه دارید
- فقط به ادمین‌های مورد اعتماد دسترسی دهید
- فایل `.env` را در `.gitignore` قرار دهید

## 🐛 عیب‌یابی

### مشکلات رایج:

1. **ربات پاسخ نمی‌دهد:**
   - توکن ربات را بررسی کنید
   - اتصال اینترنت را چک کنید

2. **پیام‌ها ارسال نمی‌شوند:**
   - محتوا در دیتابیس موجود باشد
   - زمان‌بند درست کار کند

3. **خطای دیتابیس:**
   - مجوزهای فایل دیتابیس را بررسی کنید
   - فضای دیسک کافی باشد

## 📝 لاگ‌ها

لاگ‌های ربات در فایل `motivational_bot.log` ذخیره می‌شوند.

## 🤝 مشارکت

برای مشارکت در پروژه:

1. پروژه را Fork کنید
2. شاخه جدید ایجاد کنید
3. تغییرات را Commit کنید
4. Pull Request ارسال کنید

## 📄 مجوز

این پروژه تحت مجوز MIT منتشر شده است.

## 📞 پشتیبانی

برای سوالات و پشتیبانی:
- Issue در GitHub ایجاد کنید
- ایمیل ارسال کنید

---

**نکته:** این ربات برای اهداف آموزشی و انگیزشی طراحی شده است. از آن به صورت مسئولانه استفاده کنید.
