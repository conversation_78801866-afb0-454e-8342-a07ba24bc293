#!/usr/bin/env python3
"""
Send a direct message to a specific user (not through bot commands)
"""

import os
import sys
import asyncio
import logging
from dotenv import load_dotenv
from telegram import Bot

from database import DatabaseManager

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)

logger = logging.getLogger(__name__)

async def send_direct_message():
    """Send direct message to user"""
    
    bot_token = os.getenv('BOT_TOKEN')
    if not bot_token:
        logger.error("BOT_TOKEN not found!")
        return
    
    # Your chat ID (the user who should receive the message)
    # This should be your personal chat ID with the bot
    target_chat_id = 222030871  # Replace with your actual chat ID
    
    logger.info(f"📤 Sending direct message to chat ID: {target_chat_id}")
    
    # Initialize components
    db_manager = DatabaseManager()
    bot = Bot(token=bot_token)
    
    # Get random content
    content = db_manager.get_random_content()
    if not content:
        logger.error("❌ No content found in database!")
        return
    
    content_id, content_type, text, file_path, file_id = content
    logger.info(f"📝 Sending content: {text[:50]}...")
    
    try:
        # Send message directly to the user
        if content_type == 'text':
            message = await bot.send_message(
                chat_id=target_chat_id,
                text=f"🌟 پیام انگیزشی روزانه:\n\n{text}\n\n💫 موفق باشید!",
                parse_mode='HTML'
            )
        elif content_type == 'photo' and file_path:
            message = await bot.send_photo(
                chat_id=target_chat_id,
                photo=file_path,
                caption=f"🌟 پیام انگیزشی روزانه:\n\n{text}\n\n💫 موفق باشید!"
            )
        elif content_type == 'video' and file_path:
            message = await bot.send_video(
                chat_id=target_chat_id,
                video=file_path,
                caption=f"🌟 پیام انگیزشی روزانه:\n\n{text}\n\n💫 موفق باشید!"
            )
        elif content_type == 'audio' and file_path:
            message = await bot.send_audio(
                chat_id=target_chat_id,
                audio=file_path,
                caption=f"🌟 پیام انگیزشی روزانه:\n\n{text}\n\n💫 موفق باشید!"
            )
        else:
            # Fallback to text
            message = await bot.send_message(
                chat_id=target_chat_id,
                text=f"🌟 پیام انگیزشی روزانه:\n\n{text}\n\n💫 موفق باشید!",
                parse_mode='HTML'
            )
        
        logger.info(f"✅ Direct message sent successfully!")
        logger.info(f"📱 Message ID: {message.message_id}")
        logger.info(f"📅 Sent at: {message.date}")
        
    except Exception as e:
        logger.error(f"❌ Failed to send direct message: {e}")

if __name__ == "__main__":
    print("""
📤 Sending Direct Message

This script will send a motivational message directly to your personal chat.
The message will appear as if sent by the bot, but directly to you.

Target Chat ID: 222030871
    """)
    
    asyncio.run(send_direct_message())
