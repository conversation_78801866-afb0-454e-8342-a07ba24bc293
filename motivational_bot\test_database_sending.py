#!/usr/bin/env python3
"""
Test sending messages to database users
"""

import os
import asyncio
import logging
from dotenv import load_dotenv
from telegram import Bo<PERSON>

from database import DatabaseManager

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_database_sending():
    """Test sending message to all database users"""
    
    bot_token = os.getenv('BOT_TOKEN')
    if not bot_token:
        print("❌ BOT_TOKEN not found!")
        return
    
    bot = Bot(token=bot_token)
    db = DatabaseManager()
    
    print("""
🧪 TESTING DATABASE USER MESSAGING

This will:
1. 📋 Get all active users from database
2. 📤 Send test message to each user
3. 📊 Report success/failure rates
    """)
    
    # Get active users
    active_users = db.get_active_users()
    
    print(f"""
📊 Database Status:
   👥 Active users: {len(active_users)}
   🆔 User IDs: {active_users}
    """)
    
    if not active_users:
        print("⚠️ No active users in database!")
        print("💡 Use /start command in bot to add yourself")
        return
    
    # Create test message
    test_message = """🧪 تست ارسال پیام

✅ این پیام از دیتابیس کاربران ارسال شده است
🤖 ربات انگیزشی شما کار می‌کند!

📊 اگر این پیام را دریافت کردید، سیستم درست کار می‌کند.

#تست #ربات_انگیزشی"""
    
    print(f"📤 Sending test message to {len(active_users)} users...")
    
    success_count = 0
    failed_count = 0
    failed_users = []
    
    # Send to all users
    for user_id in active_users:
        try:
            await bot.send_message(
                chat_id=user_id,
                text=test_message,
                parse_mode='HTML'
            )
            
            success_count += 1
            print(f"✅ Message sent to user {user_id}")
            await asyncio.sleep(0.5)  # Rate limiting
            
        except Exception as e:
            failed_count += 1
            failed_users.append((user_id, str(e)))
            print(f"❌ Failed to send to user {user_id}: {e}")
            
            # Deactivate users who blocked the bot
            if "bot was blocked" in str(e).lower():
                db.deactivate_user(user_id)
                print(f"🚫 Deactivated user {user_id} (blocked bot)")
            elif "chat not found" in str(e).lower():
                db.deactivate_user(user_id)
                print(f"🚫 Deactivated user {user_id} (chat not found)")
    
    print(f"""
🎯 TEST RESULTS:
   ✅ Successful sends: {success_count}
   ❌ Failed sends: {failed_count}
   📊 Success rate: {(success_count/(success_count+failed_count)*100):.1f}%
    """)
    
    if failed_users:
        print("❌ Failed users:")
        for user_id, error in failed_users:
            print(f"   👤 {user_id}: {error}")
    
    # Check updated database status
    updated_users = db.get_active_users()
    print(f"""
📊 Updated Database Status:
   👥 Active users: {len(updated_users)}
   🆔 User IDs: {updated_users}
    """)
    
    if success_count > 0:
        print("🎉 DATABASE MESSAGING WORKS PERFECTLY!")
        print("✅ Your bot can send messages to database users")
    else:
        print("⚠️ No messages were sent successfully")
        print("💡 Make sure users haven't blocked the bot")

async def main():
    """Main function"""
    try:
        await test_database_sending()
    except Exception as e:
        logger.error(f"Error in test: {e}")

if __name__ == "__main__":
    asyncio.run(main())
