#!/usr/bin/env python3
"""
Read all messages from channel and add them to tracker
Since bot is admin, it can read channel messages
"""

import os
import asyncio
import logging
from dotenv import load_dotenv
from telegram import Bo<PERSON>
from telegram.error import TelegramError

from channel_message_tracker import ChannelMessageTracker

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)

logger = logging.getLogger(__name__)

class ChannelMessageReader:
    def __init__(self, bot_token: str, channel_id: str):
        self.bot = Bot(token=bot_token)
        self.channel_id = channel_id
        self.tracker = ChannelMessageTracker()
    
    async def get_channel_message(self, message_id: int):
        """Get a specific message from channel"""
        try:
            # Try to get the message
            message = await self.bot.get_chat(self.channel_id)
            
            # Since we can't directly get message by ID, we'll use a different approach
            # We'll try to forward the message to get its content
            
            # For now, let's try to get recent messages using getUpdates
            # This is a workaround - in production you might want to use different methods
            
            logger.info(f"Attempting to read message {message_id} from {self.channel_id}")
            
            # Try to copy the message to see if it exists
            try:
                # This will fail if message doesn't exist
                copied_message = await self.bot.copy_message(
                    chat_id=self.channel_id,  # Copy to same channel (will fail but tells us if message exists)
                    from_chat_id=self.channel_id,
                    message_id=message_id
                )
                return True  # Message exists
            except TelegramError as e:
                if "message not found" in str(e).lower():
                    return False  # Message doesn't exist
                elif "can't copy message" in str(e).lower():
                    return True  # Message exists but can't be copied (normal)
                else:
                    logger.warning(f"Unexpected error checking message {message_id}: {e}")
                    return True  # Assume it exists
                    
        except Exception as e:
            logger.error(f"Error checking message {message_id}: {e}")
            return False
    
    async def scan_channel_messages(self, start_id: int = 1, end_id: int = 1000):
        """Scan channel for existing messages and add them to tracker"""
        
        print(f"""
🔍 Scanning Channel Messages

Channel: {self.channel_id}
Range: {start_id} to {end_id}
        """)
        
        existing_messages = []
        checked_count = 0
        
        print("🚀 Starting scan...")
        
        for message_id in range(start_id, end_id + 1):
            try:
                checked_count += 1
                
                # Show progress every 50 messages
                if checked_count % 50 == 0:
                    print(f"📊 Checked {checked_count} messages, found {len(existing_messages)} existing")
                
                # Check if message exists
                exists = await self.get_channel_message(message_id)
                
                if exists:
                    existing_messages.append(message_id)
                    logger.info(f"✅ Found message {message_id}")
                
                # Small delay to avoid rate limiting
                await asyncio.sleep(0.1)
                
            except Exception as e:
                logger.error(f"Error checking message {message_id}: {e}")
                continue
        
        print(f"""
📋 Scan Results:
   🔍 Checked: {checked_count} message IDs
   ✅ Found: {len(existing_messages)} existing messages
   📝 Message IDs: {existing_messages[:20]}{'...' if len(existing_messages) > 20 else ''}
        """)
        
        if existing_messages:
            # Add to tracker
            added_count = self.tracker.add_multiple_messages(existing_messages, self.channel_id)
            print(f"💾 Added {added_count}/{len(existing_messages)} messages to tracker")
            
            # Show stats
            stats = self.tracker.get_stats()
            print(f"""
📊 Tracker Stats:
   Total messages: {stats.get('total_messages', 0)}
   Unused messages: {stats.get('unused_messages', 0)}
   Used messages: {stats.get('used_messages', 0)}
            """)
        
        return existing_messages
    
    async def get_recent_messages(self, limit: int = 100):
        """Try to get recent messages using different approach"""
        try:
            print(f"🔍 Trying to get recent messages from {self.channel_id}")
            
            # Get chat info
            chat = await self.bot.get_chat(self.channel_id)
            print(f"📺 Channel: {chat.title}")
            print(f"👥 Members: {chat.get_member_count() if hasattr(chat, 'get_member_count') else 'Unknown'}")
            
            # Since we can't directly get messages, let's try a range of recent IDs
            # Most channels have sequential message IDs
            
            # Try to find the latest message ID by testing backwards from a high number
            max_test_id = 2000  # Start testing from this ID
            latest_id = None
            
            print(f"🔍 Looking for latest message ID...")
            
            for test_id in range(max_test_id, 0, -10):  # Test every 10th ID backwards
                try:
                    exists = await self.get_channel_message(test_id)
                    if exists:
                        latest_id = test_id
                        print(f"✅ Found message at ID {test_id}")
                        break
                    await asyncio.sleep(0.05)
                except:
                    continue
            
            if latest_id:
                print(f"🎯 Latest found message ID: {latest_id}")
                
                # Now scan backwards to find more messages
                found_messages = []
                for msg_id in range(latest_id, max(1, latest_id - limit), -1):
                    try:
                        exists = await self.get_channel_message(msg_id)
                        if exists:
                            found_messages.append(msg_id)
                        await asyncio.sleep(0.05)
                    except:
                        continue
                
                found_messages.sort()  # Sort in ascending order
                print(f"📝 Found {len(found_messages)} recent messages: {found_messages[:10]}{'...' if len(found_messages) > 10 else ''}")
                
                return found_messages
            else:
                print("❌ Could not find any messages")
                return []
                
        except Exception as e:
            logger.error(f"Error getting recent messages: {e}")
            return []

async def main():
    """Main function"""
    bot_token = os.getenv('BOT_TOKEN')
    channel_id = os.getenv('CHANNEL_ID')
    
    if not bot_token or not channel_id:
        logger.error("BOT_TOKEN or CHANNEL_ID not found!")
        return
    
    reader = ChannelMessageReader(bot_token, channel_id)
    
    print(f"""
📖 Channel Message Reader

Since your bot is admin of the channel, we can read messages!

Channel: {channel_id}
Bot: Admin access ✅

Options:
1. Scan specific range (e.g., 1-500)
2. Find recent messages automatically
3. Test specific message ID
    """)
    
    try:
        choice = input("Choose option (1/2/3): ").strip()
        
        if choice == "1":
            start = int(input("Start ID (e.g., 1): ").strip() or "1")
            end = int(input("End ID (e.g., 500): ").strip() or "500")
            
            messages = await reader.scan_channel_messages(start, end)
            print(f"✅ Scan complete! Found {len(messages)} messages")
            
        elif choice == "2":
            limit = int(input("How many recent messages to find (e.g., 100): ").strip() or "100")
            
            messages = await reader.get_recent_messages(limit)
            if messages:
                # Add to tracker
                added = reader.tracker.add_multiple_messages(messages, channel_id)
                print(f"✅ Added {added} messages to tracker")
            
        elif choice == "3":
            msg_id = int(input("Enter message ID to test: ").strip())
            exists = await reader.get_channel_message(msg_id)
            print(f"Message {msg_id}: {'✅ Exists' if exists else '❌ Not found'}")
        
        else:
            print("❌ Invalid choice")
            
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
    except Exception as e:
        logger.error(f"Error in main: {e}")

if __name__ == "__main__":
    asyncio.run(main())
