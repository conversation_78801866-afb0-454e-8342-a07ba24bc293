#!/usr/bin/env python3
"""
Check database users and their details
"""

import sqlite3
import os

def check_database():
    """Check database users"""
    
    db_path = "motivational_bot.db"
    
    if not os.path.exists(db_path):
        print("❌ Database file not found!")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Get all users
        cursor.execute("SELECT * FROM users")
        users = cursor.fetchall()
        
        print(f"""
📊 DATABASE USERS CHECK

Database file: {db_path}
        """)
        
        if not users:
            print("⚠️ No users found in database!")
        else:
            print(f"👥 Found {len(users)} users:")
            print("=" * 50)
            
            for user in users:
                user_id, username, first_name, last_name, is_active, created_at = user
                print(f"""
🆔 User ID: {user_id}
👤 Username: {username or 'None'}
📝 First Name: {first_name or 'None'}
📝 Last Name: {last_name or 'None'}
✅ Active: {'Yes' if is_active else 'No'}
📅 Created: {created_at}
                """)
        
        # Get active users only
        cursor.execute("SELECT user_id FROM users WHERE is_active = 1")
        active_users = cursor.fetchall()
        
        print(f"""
📋 ACTIVE USERS SUMMARY:
   Total users: {len(users)}
   Active users: {len(active_users)}
   Active IDs: {[user[0] for user in active_users]}
        """)
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error checking database: {e}")

if __name__ == "__main__":
    check_database()
