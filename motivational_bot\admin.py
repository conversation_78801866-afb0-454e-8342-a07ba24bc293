import os
import logging
from typing import List
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import Con<PERSON>Types, CommandHandler, MessageHandler, CallbackQueryHandler, filters
from telegram.constants import ParseMode

from database import DatabaseManager
from content_manager import ContentManager

logger = logging.getLogger(__name__)

class AdminPanel:
    def __init__(self, db_manager: DatabaseManager, content_manager: ContentManager, admin_chat_ids: List[int]):
        self.db = db_manager
        self.content_manager = content_manager
        self.admin_chat_ids = admin_chat_ids
    
    def is_admin(self, chat_id: int) -> bool:
        """Check if user is admin"""
        return chat_id in self.admin_chat_ids
    
    async def admin_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /admin command"""
        chat_id = update.effective_chat.id
        
        if not self.is_admin(chat_id):
            await update.message.reply_text("❌ شما دسترسی ادمین ندارید.")
            return
        
        keyboard = [
            [InlineKeyboardButton("📊 آمار کلی", callback_data="admin_stats")],
            [InlineKeyboardButton("📝 مدیریت محتوا", callback_data="admin_content")],
            [InlineKeyboardButton("👥 مدیریت کاربران", callback_data="admin_users")],
            [InlineKeyboardButton("🔄 ارسال پیام تست", callback_data="admin_test_message")]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        await update.message.reply_text(
            "🔧 پنل مدیریت ربات انگیزشی\n\nیکی از گزینه‌های زیر را انتخاب کنید:",
            reply_markup=reply_markup
        )
    
    async def admin_callback(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle admin panel callbacks"""
        query = update.callback_query
        chat_id = query.message.chat_id
        
        if not self.is_admin(chat_id):
            await query.answer("❌ دسترسی غیرمجاز")
            return
        
        await query.answer()
        
        if query.data == "admin_stats":
            await self._show_stats(query)
        elif query.data == "admin_content":
            await self._show_content_management(query)
        elif query.data == "admin_users":
            await self._show_user_management(query)
        elif query.data == "admin_test_message":
            await self._send_test_message(query)
    
    async def _show_stats(self, query):
        """Show bot statistics"""
        total_users = self.db.get_user_count()
        content_stats = self.content_manager.get_content_stats()
        
        stats_text = f"""
📊 آمار کلی ربات:

👥 تعداد کاربران فعال: {total_users}

📝 محتوای موجود:
• متن: {content_stats.get('text', 0)}
• عکس: {content_stats.get('photo', 0)}
• ویدئو: {content_stats.get('video', 0)}
• موسیقی: {content_stats.get('audio', 0)}
• مجموع: {content_stats.get('total', 0)}

⏰ زمان ارسال: هر روز ساعت 8 صبح
        """
        
        keyboard = [[InlineKeyboardButton("🔙 بازگشت", callback_data="admin_back")]]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        await query.edit_message_text(stats_text, reply_markup=reply_markup)
    
    async def _show_content_management(self, query):
        """Show content management options"""
        keyboard = [
            [InlineKeyboardButton("➕ افزودن متن", callback_data="admin_add_text")],
            [InlineKeyboardButton("📋 مشاهده محتوا", callback_data="admin_view_content")],
            [InlineKeyboardButton("🔙 بازگشت", callback_data="admin_back")]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        text = """
📝 مدیریت محتوا:

برای افزودن محتوای جدید:
• متن: از دکمه "افزودن متن" استفاده کنید
• عکس/ویدئو/موسیقی: فایل را مستقیماً ارسال کنید

توجه: فایل‌های ارسالی به صورت خودکار به محتوای ربات اضافه می‌شوند.
        """
        
        await query.edit_message_text(text, reply_markup=reply_markup)
    
    async def _show_user_management(self, query):
        """Show user management options"""
        active_users = self.db.get_active_users()
        
        text = f"""
👥 مدیریت کاربران:

تعداد کاربران فعال: {len(active_users)}

عملیات موجود:
• مشاهده لیست کاربران
• ارسال پیام به همه کاربران
• آمار استفاده
        """
        
        keyboard = [[InlineKeyboardButton("🔙 بازگشت", callback_data="admin_back")]]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        await query.edit_message_text(text, reply_markup=reply_markup)
    
    async def _send_test_message(self, query):
        """Send test message to admin"""
        # This would trigger the daily message function for testing
        await query.edit_message_text("🔄 در حال ارسال پیام تست...")
        
        # Here you would call the send_daily_message function
        # For now, just show a confirmation
        keyboard = [[InlineKeyboardButton("🔙 بازگشت", callback_data="admin_back")]]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        await query.edit_message_text(
            "✅ پیام تست ارسال شد!\n\nاگر پیامی دریافت نکردید، احتمالاً محتوایی در دیتابیس موجود نیست.",
            reply_markup=reply_markup
        )
    
    async def handle_media_upload(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle media uploads from admin"""
        chat_id = update.effective_chat.id
        
        if not self.is_admin(chat_id):
            return
        
        message = update.message
        
        try:
            if message.photo:
                # Handle photo
                file_id = message.photo[-1].file_id
                caption = message.caption or "تصویر انگیزشی"
                
                success = self.content_manager.add_media_content("photo", file_id, caption)
                if success:
                    await message.reply_text("✅ تصویر با موفقیت اضافه شد!")
                else:
                    await message.reply_text("❌ خطا در افزودن تصویر")
            
            elif message.video:
                # Handle video
                file_id = message.video.file_id
                caption = message.caption or "ویدئو انگیزشی"
                
                success = self.content_manager.add_media_content("video", file_id, caption)
                if success:
                    await message.reply_text("✅ ویدئو با موفقیت اضافه شد!")
                else:
                    await message.reply_text("❌ خطا در افزودن ویدئو")
            
            elif message.audio or message.voice:
                # Handle audio
                file_id = message.audio.file_id if message.audio else message.voice.file_id
                caption = message.caption or "موسیقی انگیزشی"
                
                success = self.content_manager.add_media_content("audio", file_id, caption)
                if success:
                    await message.reply_text("✅ فایل صوتی با موفقیت اضافه شد!")
                else:
                    await message.reply_text("❌ خطا در افزودن فایل صوتی")
        
        except Exception as e:
            logger.error(f"Error handling media upload: {e}")
            await message.reply_text("❌ خطا در پردازش فایل")
    
    def get_handlers(self):
        """Get admin handlers for the bot"""
        return [
            CommandHandler("admin", self.admin_command),
            CallbackQueryHandler(self.admin_callback, pattern="^admin_"),
            MessageHandler(filters.PHOTO | filters.VIDEO | filters.AUDIO | filters.VOICE, self.handle_media_upload)
        ]
