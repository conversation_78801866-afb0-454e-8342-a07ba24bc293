#!/usr/bin/env python3
"""
Test the daily message forwarding system
"""

import os
import asyncio
import logging
from dotenv import load_dotenv
from telegram import <PERSON><PERSON>

from database import DatabaseManager
from channel_message_tracker import ChannelMessageTracker

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)

logger = logging.getLogger(__name__)

async def test_daily_forward():
    """Test the daily forwarding system"""
    
    bot_token = os.getenv('BOT_TOKEN')
    channel_id = os.getenv('CHANNEL_ID')
    
    if not bot_token or not channel_id:
        logger.error("BOT_TOKEN or CHANNEL_ID not found!")
        return
    
    print(f"""
🧪 Testing Daily Message Forwarding System

This simulates what happens every day at 8:00 AM:
1. Get next unused message from channel tracker
2. Forward it to all database users
3. Mark message as used

Channel: {channel_id}
    """)
    
    # Initialize components
    bot = Bot(token=bot_token)
    db = DatabaseManager()
    tracker = ChannelMessageTracker()
    
    # Get active users
    active_users = db.get_active_users()
    print(f"👥 Active users: {len(active_users)}")
    
    if not active_users:
        print("⚠️ No active users found!")
        return
    
    # Get next unused message
    unused_message = tracker.get_unused_message()
    if not unused_message:
        print("⚠️ No unused messages available!")
        print("💡 Run setup_channel_messages.py to add messages")
        return
    
    message_id, tracked_channel_id = unused_message
    print(f"📤 Next message to forward: {message_id} from {channel_id}")
    
    try:
        success_count = 0
        failed_count = 0
        
        print(f"🚀 Starting forward process...")
        
        # Forward to all users
        for user_id in active_users:
            try:
                forwarded_message = await bot.forward_message(
                    chat_id=user_id,
                    from_chat_id=channel_id,
                    message_id=message_id
                )
                
                success_count += 1
                print(f"✅ Forwarded to user {user_id}")
                
                # Small delay
                await asyncio.sleep(0.1)
                
            except Exception as e:
                failed_count += 1
                print(f"❌ Failed to forward to user {user_id}: {e}")
                
                # Handle specific errors
                if "bot was blocked" in str(e).lower():
                    print(f"🚫 User {user_id} blocked the bot - would deactivate")
                elif "chat not found" in str(e).lower():
                    print(f"👻 User {user_id} not found - would deactivate")
                elif "message not found" in str(e).lower():
                    print(f"📭 Message {message_id} not found in channel")
                    break
        
        # Mark as used if successful
        if success_count > 0:
            tracker.mark_message_used(message_id)
            print(f"✅ Marked message {message_id} as used")
        
        print(f"""
🎯 Test Results:
   ✅ Successful forwards: {success_count}
   ❌ Failed forwards: {failed_count}
   📊 Total users: {len(active_users)}
        """)
        
        # Show updated stats
        stats = tracker.get_stats()
        print(f"📈 Updated Tracker Stats:")
        print(f"   Total messages: {stats.get('total_messages', 0)}")
        print(f"   Used messages: {stats.get('used_messages', 0)}")
        print(f"   Unused messages: {stats.get('unused_messages', 0)}")
        
        # Show next message
        next_unused = tracker.get_unused_message()
        if next_unused:
            next_id, _ = next_unused
            print(f"📤 Next message for tomorrow: {next_id}")
        else:
            print(f"⚠️ No more unused messages! Add more to continue daily forwards.")
        
        print(f"""
✅ Test Complete!

This is exactly what will happen every day at 8:00 AM.
The bot will automatically:
1. Take the next unused message from your channel
2. Forward it to all active subscribers
3. Mark it as used so it won't be sent again

Your daily motivation system is ready! 🚀
        """)
        
    except Exception as e:
        logger.error(f"Error in test: {e}")

if __name__ == "__main__":
    asyncio.run(test_daily_forward())
