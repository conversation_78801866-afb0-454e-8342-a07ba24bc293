#!/usr/bin/env python3
"""
Run bot with new message capture system
"""

import os
import asyncio
import logging
from dotenv import load_dotenv
from telegram import Update
from telegram.ext import Application, CommandHandler, MessageHandler, filters, ContextTypes

from database import DatabaseManager
from channel_message_tracker import ChannelMessageTracker
from bot import MotivationalBot

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)

logger = logging.getLogger(__name__)

class BotWithChannelCapture:
    def __init__(self, bot_token: str, channel_id: str):
        self.bot_token = bot_token
        self.channel_id = channel_id
        self.db = DatabaseManager()
        self.tracker = ChannelMessageTracker()
        self.stored_messages = {}  # Store real message content
        self.motivational_bot = MotivationalBot(bot_token, channel_id)
    
    async def handle_channel_post(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Capture new channel messages"""
        try:
            if not update.channel_post:
                return
            
            message = update.channel_post
            channel_username = f"@{message.chat.username}" if message.chat.username else str(message.chat.id)
            
            # Check if this is our target channel
            if channel_username != self.channel_id:
                return
            
            message_id = message.message_id
            logger.info(f"📨 New channel message captured: {message_id}")
            
            # Extract real content
            real_content = {
                'message_id': message_id,
                'text': message.text or message.caption or "پیام حاوی رسانه",
                'type': 'text' if message.text else 'media',
                'has_media': bool(message.photo or message.video or message.document or message.audio),
                'date': message.date.isoformat() if message.date else None,
                'channel_id': channel_username
            }
            
            # Store real content
            self.stored_messages[message_id] = real_content
            
            # Add to tracker
            self.tracker.add_message(message_id, channel_username)
            
            print(f"""
🎉 NEW CHANNEL MESSAGE CAPTURED!

🆔 Message ID: {message_id}
📝 Content: {real_content['text'][:100]}...
📅 Date: {real_content['date']}
💾 Stored for daily sending

📊 Total stored messages: {len(self.stored_messages)}
            """)
            
        except Exception as e:
            logger.error(f"Error capturing channel message: {e}")
    
    def get_real_message_content(self, message_id: int):
        """Get real content of captured message"""
        return self.stored_messages.get(message_id)
    
    async def send_real_content_to_users(self, message_id: int):
        """Send real channel content to database users"""
        try:
            # Get active users
            active_users = self.db.get_active_users()
            if not active_users:
                logger.warning("No active users to send to")
                return False
            
            # Get real content
            real_content = self.get_real_message_content(message_id)
            if not real_content:
                logger.warning(f"No real content found for message {message_id}")
                # Fall back to basic message
                real_content = {
                    'text': f"پیام انگیزشی شماره {message_id} از کانال",
                    'type': 'fallback'
                }
            
            logger.info(f"📤 Sending REAL content of message {message_id} to {len(active_users)} users")
            
            # Create beautiful message with real content
            from datetime import datetime
            current_time = datetime.now()
            time_str = current_time.strftime("%H:%M")
            date_str = current_time.strftime("%Y/%m/%d")
            day_name = current_time.strftime("%A")
            
            # Persian day names
            persian_days = {
                'Monday': 'دوشنبه', 'Tuesday': 'سه‌شنبه', 'Wednesday': 'چهارشنبه',
                'Thursday': 'پنج‌شنبه', 'Friday': 'جمعه', 'Saturday': 'شنبه', 'Sunday': 'یکشنبه'
            }
            persian_day = persian_days.get(day_name, day_name)
            
            # Use REAL content from channel
            message_text = f"""🌟 پیام انگیزشی روزانه

{real_content['text']}

━━━━━━━━━━━━━━━━━━━━
📅 {persian_day} | {date_str}
🕐 {time_str}
🆔 پیام شماره: {message_id}
📺 از کانال: @angizeshivahid

#انگیزشی #موفقیت #روزانه"""

            success_count = 0
            failed_count = 0
            
            # Send to all database users
            for user_id in active_users:
                try:
                    await context.bot.send_message(
                        chat_id=user_id,
                        text=message_text,
                        parse_mode='HTML'
                    )
                    
                    success_count += 1
                    logger.info(f"✅ Real content sent to user {user_id}")
                    await asyncio.sleep(0.1)
                    
                except Exception as e:
                    failed_count += 1
                    logger.error(f"❌ Failed to send to user {user_id}: {e}")
                    
                    if "bot was blocked" in str(e).lower():
                        self.db.deactivate_user(user_id)
                    elif "chat not found" in str(e).lower():
                        self.db.deactivate_user(user_id)
            
            logger.info(f"🎯 Real content sending completed: {success_count} success, {failed_count} failed")
            return success_count > 0
            
        except Exception as e:
            logger.error(f"Error sending real content: {e}")
            return False
    
    async def start_bot_with_capture(self):
        """Start bot with channel message capture"""
        try:
            # Create application
            application = Application.builder().token(self.bot_token).build()
            
            # Add channel post handler
            application.add_handler(
                MessageHandler(filters.UpdateType.CHANNEL_POST, self.handle_channel_post)
            )
            
            # Add regular bot handlers
            await self.motivational_bot.setup_handlers(application)
            
            print(f"""
🚀 MOTIVATIONAL BOT WITH REAL CHANNEL CONTENT

✅ Bot Features:
   🤖 Regular bot commands (/start, /help, etc.)
   📨 Captures NEW channel messages
   💾 Stores REAL content
   📤 Sends to database users
   ⏰ Daily scheduling at 8:00 AM

📺 Channel: {self.channel_id}
👥 Database users: {len(self.db.get_active_users())}
💾 Stored messages: {len(self.stored_messages)}

🎯 READY TO CAPTURE REAL CHANNEL MESSAGES!
💡 Post a new message in your channel to test!

Press Ctrl+C to stop.
            """)
            
            # Start polling
            await application.run_polling(drop_pending_updates=True)
            
        except KeyboardInterrupt:
            print("\n👋 Bot stopped!")
        except Exception as e:
            logger.error(f"Error running bot: {e}")

async def main():
    """Main function"""
    bot_token = os.getenv('BOT_TOKEN')
    channel_id = os.getenv('CHANNEL_ID')
    
    if not bot_token or not channel_id:
        logger.error("BOT_TOKEN or CHANNEL_ID not found!")
        return
    
    bot_with_capture = BotWithChannelCapture(bot_token, channel_id)
    
    print(f"""
🎯 STARTING ADVANCED MOTIVATIONAL BOT

This system will:
1. 🤖 Run the regular motivational bot
2. 📨 Capture NEW messages from your channel
3. 💾 Store REAL content of messages
4. 📤 Send REAL content to database users
5. ⏰ Schedule daily messages at 8:00 AM

Ready to capture and send REAL channel content!
    """)
    
    await bot_with_capture.start_bot_with_capture()

if __name__ == "__main__":
    asyncio.run(main())
