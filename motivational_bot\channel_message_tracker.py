#!/usr/bin/env python3
"""
Track and manage channel messages for forwarding
"""

import os
import sqlite3
import logging
from datetime import datetime
from typing import List, Optional, Tuple

logger = logging.getLogger(__name__)

class ChannelMessageTracker:
    def __init__(self, db_path: str = "motivational_bot.db"):
        self.db_path = db_path
        self.init_database()
    
    def init_database(self):
        """Initialize the channel messages tracking table"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Create table for tracking channel messages
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS channel_messages (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    message_id INTEGER NOT NULL UNIQUE,
                    channel_id TEXT NOT NULL,
                    date_posted DATETIME,
                    forwarded_count INTEGER DEFAULT 0,
                    last_forwarded DATETIME,
                    is_used BOOLEAN DEFAULT FALSE,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            conn.commit()
            conn.close()
            logger.info("Channel message tracker database initialized")
            
        except Exception as e:
            logger.error(f"Error initializing channel message tracker: {e}")
    
    def add_channel_message(self, message_id: int, channel_id: str, date_posted: datetime = None) -> bool:
        """Add a new channel message to track"""
        try:
            if date_posted is None:
                date_posted = datetime.now()
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT OR IGNORE INTO channel_messages 
                (message_id, channel_id, date_posted)
                VALUES (?, ?, ?)
            ''', (message_id, channel_id, date_posted))
            
            conn.commit()
            conn.close()
            
            logger.info(f"Added channel message {message_id} to tracker")
            return True
            
        except Exception as e:
            logger.error(f"Error adding channel message: {e}")
            return False
    
    def get_unused_message(self) -> Optional[Tuple[int, str]]:
        """Get the oldest unused message from channel"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT message_id, channel_id 
                FROM channel_messages 
                WHERE is_used = FALSE 
                ORDER BY date_posted ASC 
                LIMIT 1
            ''')
            
            result = cursor.fetchone()
            conn.close()
            
            if result:
                return result[0], result[1]  # message_id, channel_id
            return None
            
        except Exception as e:
            logger.error(f"Error getting unused message: {e}")
            return None
    
    def mark_message_used(self, message_id: int) -> bool:
        """Mark a message as used"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                UPDATE channel_messages 
                SET is_used = TRUE, 
                    forwarded_count = forwarded_count + 1,
                    last_forwarded = CURRENT_TIMESTAMP
                WHERE message_id = ?
            ''', (message_id,))
            
            conn.commit()
            conn.close()
            
            logger.info(f"Marked message {message_id} as used")
            return True
            
        except Exception as e:
            logger.error(f"Error marking message as used: {e}")
            return False
    
    def get_all_messages(self) -> List[Tuple]:
        """Get all tracked messages"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT message_id, channel_id, date_posted, forwarded_count, 
                       last_forwarded, is_used, created_at
                FROM channel_messages 
                ORDER BY date_posted DESC
            ''')
            
            results = cursor.fetchall()
            conn.close()
            
            return results
            
        except Exception as e:
            logger.error(f"Error getting all messages: {e}")
            return []
    
    def reset_all_messages(self) -> bool:
        """Reset all messages to unused (for testing)"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                UPDATE channel_messages
                SET is_used = FALSE
            ''')

            conn.commit()
            conn.close()

            logger.info("Reset all messages to unused")
            return True

        except Exception as e:
            logger.error(f"Error resetting messages: {e}")
            return False

    def clear_all_messages(self) -> bool:
        """Clear all messages from tracker"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute("DELETE FROM channel_messages")

            conn.commit()
            conn.close()

            logger.info("All messages cleared from tracker")
            return True

        except Exception as e:
            logger.error(f"Error clearing messages: {e}")
            return False
    
    def add_multiple_messages(self, message_ids: List[int], channel_id: str) -> int:
        """Add multiple message IDs at once"""
        success_count = 0
        
        for message_id in message_ids:
            if self.add_channel_message(message_id, channel_id):
                success_count += 1
        
        logger.info(f"Added {success_count}/{len(message_ids)} messages to tracker")
        return success_count
    
    def get_stats(self) -> dict:
        """Get statistics about tracked messages"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Total messages
            cursor.execute('SELECT COUNT(*) FROM channel_messages')
            total = cursor.fetchone()[0]
            
            # Used messages
            cursor.execute('SELECT COUNT(*) FROM channel_messages WHERE is_used = TRUE')
            used = cursor.fetchone()[0]
            
            # Unused messages
            unused = total - used
            
            # Total forwards
            cursor.execute('SELECT SUM(forwarded_count) FROM channel_messages')
            total_forwards = cursor.fetchone()[0] or 0
            
            conn.close()
            
            return {
                'total_messages': total,
                'used_messages': used,
                'unused_messages': unused,
                'total_forwards': total_forwards
            }
            
        except Exception as e:
            logger.error(f"Error getting stats: {e}")
            return {}

# Test function
async def test_tracker():
    """Test the channel message tracker"""
    tracker = ChannelMessageTracker()
    
    print("📊 Channel Message Tracker Test")
    print("=" * 40)
    
    # Add some test messages
    test_messages = [248, 249, 250, 251, 252]
    channel_id = "@angizeshivahid"
    
    print(f"➕ Adding messages: {test_messages}")
    added = tracker.add_multiple_messages(test_messages, channel_id)
    print(f"✅ Added {added} messages")
    
    # Get stats
    stats = tracker.get_stats()
    print(f"\n📈 Stats:")
    for key, value in stats.items():
        print(f"   {key}: {value}")
    
    # Get unused message
    unused = tracker.get_unused_message()
    if unused:
        message_id, channel = unused
        print(f"\n📤 Next message to forward: {message_id} from {channel}")
        
        # Mark as used
        tracker.mark_message_used(message_id)
        print(f"✅ Marked message {message_id} as used")
    
    # Get updated stats
    stats = tracker.get_stats()
    print(f"\n📈 Updated Stats:")
    for key, value in stats.items():
        print(f"   {key}: {value}")

if __name__ == "__main__":
    import asyncio
    asyncio.run(test_tracker())
