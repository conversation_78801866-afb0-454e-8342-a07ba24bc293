import asyncio
import logging
from datetime import datetime, time
from typing import Callable, Optional
import schedule

logger = logging.getLogger(__name__)

class MessageScheduler:
    def __init__(self, db_manager, send_message_callback: Callable):
        self.db = db_manager
        self.send_message_callback = send_message_callback
        self.is_running = False
        self.scheduler_task = None
        
    async def start(self):
        """Start the message scheduler"""
        if self.is_running:
            logger.warning("Scheduler is already running")
            return
            
        self.is_running = True
        
        # Schedule daily message at 8:00 AM
        schedule.every().day.at("08:00").do(self._schedule_daily_message)
        
        # Start scheduler loop
        self.scheduler_task = asyncio.create_task(self._run_scheduler())
        logger.info("Message scheduler started - Daily messages at 8:00 AM")
    
    async def stop(self):
        """Stop the message scheduler"""
        self.is_running = False
        if self.scheduler_task:
            self.scheduler_task.cancel()
            try:
                await self.scheduler_task
            except asyncio.CancelledError:
                pass
        schedule.clear()
        logger.info("Message scheduler stopped")
    
    def _schedule_daily_message(self):
        """Schedule the daily message to be sent"""
        if self.is_running:
            asyncio.create_task(self.send_message_callback())
    
    async def _run_scheduler(self):
        """Run the scheduler loop"""
        while self.is_running:
            try:
                schedule.run_pending()
                await asyncio.sleep(60)  # Check every minute
            except Exception as e:
                logger.error(f"Scheduler error: {e}")
                await asyncio.sleep(60)
    
    async def send_test_message(self):
        """Send a test message immediately"""
        logger.info("Sending test message...")
        await self.send_message_callback()
    
    def get_next_run_time(self) -> Optional[str]:
        """Get the next scheduled run time"""
        jobs = schedule.jobs
        if jobs:
            next_run = min(job.next_run for job in jobs)
            return next_run.strftime("%Y-%m-%d %H:%M:%S")
        return None
