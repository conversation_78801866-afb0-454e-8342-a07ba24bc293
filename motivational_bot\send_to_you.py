#!/usr/bin/env python3
"""
Send motivational message directly to you
"""

import os
import asyncio
import logging
from dotenv import load_dotenv
from telegram import Bo<PERSON>
from channel_message_tracker import ChannelMessageTracker

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)

logger = logging.getLogger(__name__)

async def send_motivational_message():
    """Send motivational message to you"""
    
    bot_token = os.getenv('BOT_TOKEN')
    if not bot_token:
        logger.error("BOT_TOKEN not found!")
        return
    
    bot = Bot(token=bot_token)
    tracker = ChannelMessageTracker()
    
    # Your chat ID
    your_chat_id = 222030871
    
    print("""
📤 Sending Motivational Message
━━━━━━━━━━━━━━━━━━━━
    """)
    
    # Get next unused message
    unused_message = tracker.get_unused_message()
    if not unused_message:
        print("❌ No unused messages available")
        return
    
    message_id, channel_id = unused_message
    print(f"📝 Forwarding message {message_id} from {channel_id}")
    
    try:
        # Forward the motivational message from channel
        forwarded_message = await bot.forward_message(
            chat_id=your_chat_id,
            from_chat_id=channel_id,
            message_id=message_id
        )
        
        print(f"✅ Motivational message sent successfully!")
        print(f"📤 Message ID: {forwarded_message.message_id}")
        
        # Mark as used
        tracker.mark_message_used(message_id)
        print(f"✅ Message {message_id} marked as used")
        
        # Send a follow-up message
        follow_up = """🌟 پیام انگیزشی از کانال شما!

این پیام مستقیماً از کانال @angizeshivahid به شما forward شده است.

💡 ربات شما حالا آماده است و می‌تواند:
✅ پیام‌های واقعی کانال را forward کند
✅ به personal chat کاربران ارسال کند  
✅ روزانه ساعت 8 صبح کار کند

🚀 برای اجرای ربات: python main.py"""

        await bot.send_message(
            chat_id=your_chat_id,
            text=follow_up
        )
        
        print("✅ Follow-up message sent!")
        
    except Exception as e:
        print(f"❌ Error sending message: {e}")

if __name__ == "__main__":
    asyncio.run(send_motivational_message())
