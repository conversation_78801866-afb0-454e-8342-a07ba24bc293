import os
import logging
from typing import List, Optional
from database import DatabaseManager

logger = logging.getLogger(__name__)

class ContentManager:
    def __init__(self, db_manager: DatabaseManager):
        self.db = db_manager
        self.content_dir = "content"
        self._ensure_content_directory()
        self._add_default_content()
    
    def _ensure_content_directory(self):
        """Create content directory if it doesn't exist"""
        if not os.path.exists(self.content_dir):
            os.makedirs(self.content_dir)
            logger.info(f"Created content directory: {self.content_dir}")
    
    def _add_default_content(self):
        """Add some default motivational content"""
        default_texts = [
            "🌅 هر روز فرصت جدیدی برای شروع دوباره است. امروز را با انرژی مثبت آغاز کن!",
            "💪 قدرت تو بیشتر از آن چیزی است که فکر می‌کنی. به خودت ایمان داشته باش!",
            "🎯 هدف‌هایت را مشخص کن و هر روز قدمی به سمت آن‌ها بردار.",
            "🌟 موفقیت نتیجه تلاش‌های کوچک روزانه است که هر روز تکرار می‌شوند.",
            "🚀 امروز بهترین روز برای شروع تغییرات مثبت در زندگی‌ات است!",
            "💎 تو الماسی هستی که تحت فشار می‌درخشد. هرگز تسلیم نشو!",
            "🌈 پس از هر طوفان، رنگین کمانی زیبا ظاهر می‌شود. صبور باش!",
            "🔥 شعله‌ای در درونت وجود دارد که هیچ‌کس نمی‌تواند خاموشش کند.",
            "🏔️ کوه‌ها از سنگ‌های کوچک ساخته می‌شوند. هر قدم کوچک مهم است!",
            "⭐ تو ستاره‌ای هستی که برای درخشیدن آفریده شده‌ای. بدرخش!"
        ]
        
        # Check if we already have content
        existing_content = self.db.get_random_content("text")
        if existing_content:
            return  # Content already exists
        
        # Add default texts
        for text in default_texts:
            self.db.add_content("text", content_text=text)
        
        logger.info(f"Added {len(default_texts)} default motivational texts")
    
    def add_text_content(self, text: str) -> bool:
        """Add text content to database"""
        try:
            return self.db.add_content("text", content_text=text)
        except Exception as e:
            logger.error(f"Error adding text content: {e}")
            return False
    
    def add_media_content(self, content_type: str, file_id: str, caption: str = None) -> bool:
        """Add media content (photo, video, audio) to database"""
        try:
            return self.db.add_content(content_type, content_text=caption, file_id=file_id)
        except Exception as e:
            logger.error(f"Error adding media content: {e}")
            return False
    
    def get_content_stats(self) -> dict:
        """Get content statistics"""
        try:
            import sqlite3
            with sqlite3.connect(self.db.db_path) as conn:
                cursor = conn.cursor()

                # Count by type
                cursor.execute('''
                    SELECT content_type, COUNT(*)
                    FROM content
                    WHERE is_active = 1
                    GROUP BY content_type
                ''')

                stats = {}
                for content_type, count in cursor.fetchall():
                    stats[content_type] = count

                # Total count
                cursor.execute('SELECT COUNT(*) FROM content WHERE is_active = 1')
                stats['total'] = cursor.fetchone()[0]

                return stats
        except Exception as e:
            logger.error(f"Error getting content stats: {e}")
            return {}
    
    def remove_content(self, content_id: int) -> bool:
        """Remove content by ID (soft delete)"""
        try:
            import sqlite3
            with sqlite3.connect(self.db.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('UPDATE content SET is_active = 0 WHERE id = ?', (content_id,))
                conn.commit()
                return True
        except Exception as e:
            logger.error(f"Error removing content: {e}")
            return False

    def get_all_content(self, content_type: str = None) -> List[tuple]:
        """Get all content, optionally filtered by type"""
        try:
            import sqlite3
            with sqlite3.connect(self.db.db_path) as conn:
                cursor = conn.cursor()
                if content_type:
                    cursor.execute('''
                        SELECT id, content_type, content_text, file_id, usage_count, created_date
                        FROM content
                        WHERE content_type = ? AND is_active = 1
                        ORDER BY created_date DESC
                    ''', (content_type,))
                else:
                    cursor.execute('''
                        SELECT id, content_type, content_text, file_id, usage_count, created_date
                        FROM content
                        WHERE is_active = 1
                        ORDER BY created_date DESC
                    ''')
                return cursor.fetchall()
        except Exception as e:
            logger.error(f"Error getting all content: {e}")
            return []
