#!/usr/bin/env python3
"""
Send clean motivational messages without bot interface
"""

import os
import sys
import asyncio
import logging
from datetime import datetime
from dotenv import load_dotenv
from telegram import Bo<PERSON>

from database import DatabaseManager

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)

logger = logging.getLogger(__name__)

async def send_clean_message():
    """Send clean motivational message to users"""
    
    bot_token = os.getenv('BOT_TOKEN')
    if not bot_token:
        logger.error("BOT_TOKEN not found!")
        return
    
    logger.info("📤 Sending clean motivational message...")
    
    # Initialize components
    db_manager = DatabaseManager()
    bot = Bot(token=bot_token)
    
    # Get active users
    active_users = db_manager.get_active_users()
    logger.info(f"📊 Found {len(active_users)} active users")
    
    if not active_users:
        logger.warning("⚠️ No active users found!")
        return
    
    # Get random content
    content = db_manager.get_random_content()
    if not content:
        logger.error("❌ No content found in database!")
        return
    
    content_id, content_type, text, file_path, file_id = content
    logger.info(f"📝 Sending content: {text[:50]}...")
    
    # Create clean message format
    current_time = datetime.now()
    time_str = current_time.strftime("%H:%M")
    date_str = current_time.strftime("%Y/%m/%d")
    
    # Different message formats
    formats = [
        f"""💌 {text}

🌅 صبح بخیر! امروز روز موفقیت شماست.
🕐 {time_str}""",
        
        f"""{text}

━━━━━━━━━━━━━━━━━━━━
💫 {date_str} | {time_str}
🌟 پیام انگیزشی روزانه""",
        
        f"""🎯 پیام امروز:

{text}

✨ موفق باشید!
📅 {date_str}""",
        
        f"""{text}

🌸 با آرزوی روزی پر از انرژی و موفقیت
⏰ {time_str} | 🗓 {date_str}"""
    ]
    
    # Choose a random format
    import random
    message_text = random.choice(formats)
    
    # Send to all active users
    success_count = 0
    for user_id in active_users:
        try:
            if content_type == 'text':
                await bot.send_message(
                    chat_id=user_id,
                    text=message_text,
                    parse_mode='HTML',
                    disable_notification=False
                )
            elif content_type == 'photo' and file_path:
                await bot.send_photo(
                    chat_id=user_id,
                    photo=file_path,
                    caption=message_text,
                    parse_mode='HTML'
                )
            elif content_type == 'video' and file_path:
                await bot.send_video(
                    chat_id=user_id,
                    video=file_path,
                    caption=message_text,
                    parse_mode='HTML'
                )
            elif content_type == 'audio' and file_path:
                await bot.send_audio(
                    chat_id=user_id,
                    audio=file_path,
                    caption=message_text,
                    parse_mode='HTML'
                )
            else:
                # Fallback to text
                await bot.send_message(
                    chat_id=user_id,
                    text=message_text,
                    parse_mode='HTML'
                )
            
            success_count += 1
            logger.info(f"✅ Message sent to user {user_id}")
            
            # Small delay to avoid rate limiting
            await asyncio.sleep(0.1)
            
        except Exception as e:
            logger.error(f"❌ Failed to send message to user {user_id}: {e}")
    
    logger.info(f"🎯 Clean message sent successfully to {success_count}/{len(active_users)} users!")

if __name__ == "__main__":
    print("""
💌 Sending Clean Motivational Message

This script sends a beautifully formatted motivational message
directly to all subscribers without any bot interface elements.

The message will appear clean and personal.
    """)
    
    asyncio.run(send_clean_message())
