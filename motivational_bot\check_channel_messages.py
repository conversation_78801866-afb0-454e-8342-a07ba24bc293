#!/usr/bin/env python3
"""
Check which message IDs exist in the channel
"""

import os
import asyncio
import logging
from dotenv import load_dotenv
from telegram import Bot

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)

logger = logging.getLogger(__name__)

async def check_messages():
    """Check which messages exist in channel"""
    
    bot_token = os.getenv('BOT_TOKEN')
    if not bot_token:
        logger.error("BOT_TOKEN not found!")
        return
    
    bot = Bot(token=bot_token)
    channel_id = "@angizeshivahid"
    
    print(f"""
🔍 Checking Channel Messages
━━━━━━━━━━━━━━━━━━━━
Channel: {channel_id}
    """)
    
    # Test a range of message IDs
    test_ranges = [
        (1, 10),      # Very early messages
        (100, 110),   # Early messages  
        (500, 510),   # Mid messages
        (1000, 1010), # Recent messages
        (1500, 1510), # More recent
        (1900, 1910), # Very recent
        (1950, 1960), # Latest range
        (1990, 2000), # Current range
        (2000, 2010), # Future range
    ]
    
    found_messages = []
    
    for start, end in test_ranges:
        print(f"\n🔍 Testing range {start}-{end}:")
        for msg_id in range(start, end + 1):
            try:
                # Try to forward the message to ourselves (test)
                # This will fail if message doesn't exist
                await bot.forward_message(
                    chat_id=222030871,  # Your chat ID
                    from_chat_id=channel_id,
                    message_id=msg_id
                )
                found_messages.append(msg_id)
                print(f"  ✅ Message {msg_id} exists")
                await asyncio.sleep(0.1)  # Rate limiting
                
            except Exception as e:
                if "message to forward not found" in str(e).lower():
                    print(f"  ❌ Message {msg_id} not found")
                else:
                    print(f"  ⚠️ Message {msg_id} error: {e}")
                await asyncio.sleep(0.05)
    
    print(f"""
📊 Summary:
━━━━━━━━━━━━━━━━━━━━
✅ Found {len(found_messages)} messages
📝 Message IDs: {found_messages[:20]}{'...' if len(found_messages) > 20 else ''}

💡 Use these message IDs in your setup_channel_messages.py
    """)
    
    return found_messages

if __name__ == "__main__":
    asyncio.run(check_messages())
