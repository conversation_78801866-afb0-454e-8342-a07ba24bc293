#!/usr/bin/env python3
"""
Test real message sending to verify where messages go
"""

import os
import asyncio
import logging
from dotenv import load_dotenv
from telegram import Bo<PERSON>

from database import DatabaseManager

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_real_sending():
    """Test where messages actually go"""
    
    bot_token = os.getenv('BOT_TOKEN')
    if not bot_token:
        print("❌ BOT_TOKEN not found!")
        return
    
    bot = Bot(token=bot_token)
    db = DatabaseManager()
    
    # Get bot info
    bot_info = await bot.get_me()
    print(f"""
🤖 BOT INFO:
   Name: {bot_info.first_name}
   Username: @{bot_info.username}
   ID: {bot_info.id}
    """)
    
    # Get active users
    active_users = db.get_active_users()
    print(f"""
👥 DATABASE USERS:
   Active users: {len(active_users)}
   User IDs: {active_users}
    """)
    
    if not active_users:
        print("⚠️ No active users!")
        return
    
    # Test message
    test_message = f"""🧪 تست ارسال واقعی

🆔 این پیام به کاربر {active_users[0]} ارسال می‌شود
🤖 از ربات {bot_info.username}
📍 مقصد: چت شخصی کاربر (نه چت ربات)

اگر این پیام را در چت شخصی خود می‌بینید، سیستم درست کار می‌کند! ✅"""
    
    print(f"""
📤 SENDING TEST MESSAGE:
   From: Bot @{bot_info.username} (ID: {bot_info.id})
   To: User {active_users[0]}
   Destination: User's private chat
    """)
    
    try:
        # Send to user
        message = await bot.send_message(
            chat_id=active_users[0],
            text=test_message,
            parse_mode='HTML'
        )
        
        print(f"""
✅ MESSAGE SENT SUCCESSFULLY!
   Message ID: {message.message_id}
   Chat ID: {message.chat.id}
   Chat Type: {message.chat.type}
   
🎯 The message should appear in the USER'S PRIVATE CHAT, not in the bot chat!
        """)
        
        # Verify chat info
        chat_info = await bot.get_chat(active_users[0])
        print(f"""
📋 DESTINATION CHAT INFO:
   Chat ID: {chat_info.id}
   Chat Type: {chat_info.type}
   First Name: {chat_info.first_name}
   Username: @{chat_info.username if chat_info.username else 'None'}
        """)
        
    except Exception as e:
        print(f"❌ Failed to send message: {e}")

async def main():
    """Main function"""
    try:
        await test_real_sending()
    except Exception as e:
        logger.error(f"Error in test: {e}")

if __name__ == "__main__":
    asyncio.run(main())
