#!/usr/bin/env python3
"""
Test script for Motivational Bot
This will test the bot functionality immediately
"""

import os
import sys
import asyncio
import logging
from dotenv import load_dotenv
from datetime import datetime

from bot import MotivationalBot
from database import DatabaseManager
from content_manager import ContentManager

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)

logger = logging.getLogger(__name__)

async def test_bot():
    """Test bot functionality"""
    
    bot_token = os.getenv('BOT_TOKEN')
    if not bot_token:
        logger.error("BOT_TOKEN not found!")
        return
    
    logger.info("🧪 Starting bot test...")
    
    # Initialize components
    db_manager = DatabaseManager()
    content_manager = ContentManager(db_manager)

    # Initialize the application directly
    from telegram.ext import Application, CommandHandler, CallbackQueryHandler
    application = Application.builder().token(bot_token).build()

    # Create bot instance
    bot = MotivationalBot(bot_token)
    bot.application = application

    # Add handlers
    application.add_handler(CommandHandler("start", bot.start_command))
    application.add_handler(CommandHandler("subscribe", bot.subscribe_command))
    application.add_handler(CommandHandler("unsubscribe", bot.unsubscribe_command))
    application.add_handler(CommandHandler("status", bot.status_command))
    application.add_handler(CommandHandler("help", bot.help_command))
    application.add_handler(CallbackQueryHandler(bot.button_callback))

    # Initialize and start the application
    await application.initialize()
    await application.start()
    
    logger.info("✅ Bot initialized successfully!")
    
    # Test database
    user_count = db_manager.get_user_count()
    logger.info(f"📊 Current users in database: {user_count}")
    
    # Test content
    content = db_manager.get_random_content()
    if content:
        logger.info(f"📝 Sample content available: {content[1]} - {content[2][:50]}...")
    else:
        logger.warning("⚠️ No content found in database!")
    
    # Test sending a message (if there are users)
    active_users = db_manager.get_active_users()
    if active_users:
        logger.info(f"📤 Testing message send to {len(active_users)} users...")
        await bot.send_daily_message()
        logger.info("✅ Test message sent!")
    else:
        logger.info("ℹ️ No active users to send test message to")
    
    logger.info("🎯 Bot test completed!")
    logger.info(f"⏰ Current time: {datetime.now().strftime('%H:%M:%S')}")
    logger.info("📋 To test the bot:")
    logger.info("1. Find your bot on Telegram")
    logger.info("2. Send /start command")
    logger.info("3. Subscribe with /subscribe")
    logger.info("4. Check status with /status")
    
    # Keep running for a short time to handle any incoming messages
    logger.info("🔄 Bot will run for 30 seconds to handle test messages...")

    await application.updater.start_polling()
    await asyncio.sleep(30)

    await application.stop()
    await application.shutdown()
    
    logger.info("🏁 Test completed!")

if __name__ == "__main__":
    print("""
🧪 Testing Motivational Bot...

This script will:
1. Initialize the bot
2. Test database connection
3. Check available content
4. Send test messages (if users exist)
5. Run for 30 seconds to handle commands

Make sure your .env file is configured correctly!
    """)
    
    asyncio.run(test_bot())
