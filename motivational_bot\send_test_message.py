#!/usr/bin/env python3
"""
Send a test message immediately to all subscribers
"""

import os
import sys
import asyncio
import logging
from dotenv import load_dotenv
from telegram import <PERSON><PERSON>

from database import DatabaseManager

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)

logger = logging.getLogger(__name__)

async def send_test_message():
    """Send test message to all active users"""
    
    bot_token = os.getenv('BOT_TOKEN')
    if not bot_token:
        logger.error("BOT_TOKEN not found!")
        return
    
    logger.info("📤 Sending test message...")
    
    # Initialize components
    db_manager = DatabaseManager()
    bot = Bot(token=bot_token)
    
    # Get active users
    active_users = db_manager.get_active_users()
    logger.info(f"📊 Found {len(active_users)} active users")
    
    if not active_users:
        logger.warning("⚠️ No active users found!")
        logger.info("💡 Please subscribe to the bot first:")
        logger.info("1. Find your bot on Telegram")
        logger.info("2. Send /start command")
        logger.info("3. Click Subscribe button")
        return
    
    # Get random content
    content = db_manager.get_random_content()
    if not content:
        logger.error("❌ No content found in database!")
        return
    
    content_id, content_type, text, file_path, file_id = content
    logger.info(f"📝 Sending content: {text[:50]}...")
    
    # Send to all active users
    success_count = 0
    for user_id in active_users:
        try:
            if content_type == 'text':
                await bot.send_message(
                    chat_id=user_id,
                    text=f"🌟 پیام تست انگیزشی:\n\n{text}",
                    parse_mode='HTML'
                )
            elif content_type == 'photo' and file_path:
                await bot.send_photo(
                    chat_id=user_id,
                    photo=file_path,
                    caption=f"🌟 پیام تست انگیزشی:\n\n{text}"
                )
            elif content_type == 'video' and file_path:
                await bot.send_video(
                    chat_id=user_id,
                    video=file_path,
                    caption=f"🌟 پیام تست انگیزشی:\n\n{text}"
                )
            elif content_type == 'audio' and file_path:
                await bot.send_audio(
                    chat_id=user_id,
                    audio=file_path,
                    caption=f"🌟 پیام تست انگیزشی:\n\n{text}"
                )
            else:
                # Fallback to text
                await bot.send_message(
                    chat_id=user_id,
                    text=f"🌟 پیام تست انگیزشی:\n\n{text}",
                    parse_mode='HTML'
                )
            
            success_count += 1
            logger.info(f"✅ Message sent to user {user_id}")
            
        except Exception as e:
            logger.error(f"❌ Failed to send message to user {user_id}: {e}")
    
    logger.info(f"🎯 Test message sent successfully to {success_count}/{len(active_users)} users!")

if __name__ == "__main__":
    print("""
📤 Sending Test Message

This script will send a motivational message to all active subscribers immediately.

Make sure you have subscribed to the bot first!
    """)
    
    asyncio.run(send_test_message())
