#!/usr/bin/env python3
"""
Get numeric channel ID for forwarding messages
"""

import os
import asyncio
import logging
from dotenv import load_dotenv
from telegram import Bot

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)

logger = logging.getLogger(__name__)

async def get_numeric_channel_id():
    """Get numeric channel ID"""
    
    bot_token = os.getenv('BOT_TOKEN')
    if not bot_token:
        logger.error("BOT_TOKEN not found!")
        return
    
    bot = Bot(token=bot_token)
    
    # Your channel username
    channel_username = "@angizeshivahid"
    
    try:
        # Get chat info
        chat = await bot.get_chat(chat_id=channel_username)
        
        print(f"""
✅ Channel Information:
━━━━━━━━━━━━━━━━━━━━
📋 Title: {chat.title}
🆔 Numeric ID: {chat.id}
👤 Username: {chat.username}
📝 Type: {chat.type}

💡 Use this numeric ID for forwarding:
CHANNEL_ID={chat.id}
        """)
        
        return chat.id
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return None

if __name__ == "__main__":
    asyncio.run(get_numeric_channel_id())
