import os
import logging
import asyncio
from datetime import datetime, time
from typing import Optional

from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import Application, CommandHandler, MessageHandler, CallbackQueryHandler, filters, ContextTypes
from telegram.constants import ParseMode

from database import DatabaseManager
from scheduler import MessageScheduler
from content_manager import ContentManager
from channel_message_tracker import ChannelMessageTracker

# Configure logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

class MotivationalBot:
    def __init__(self, token: str):
        self.token = token
        self.db = DatabaseManager()
        self.content_manager = ContentManager(self.db)
        self.channel_tracker = ChannelMessageTracker()
        self.scheduler = MessageScheduler(self.db, self.send_daily_message)
        self.application = None
        
    async def start_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /start command"""
        user = update.effective_user
        chat_id = update.effective_chat.id
        
        # Add user to database
        self.db.add_user(
            chat_id=chat_id,
            username=user.username,
            first_name=user.first_name,
            last_name=user.last_name
        )
        
        welcome_text = """
🌟 به ربات انگیزشی خوش آمدید! 🌟

این ربات هر روز ساعت 8 صبح برای شما:
📝 متن‌های انگیزشی
🖼️ تصاویر الهام‌بخش  
🎵 موسیقی‌های انرژی‌بخش
🎬 ویدئوهای انگیزشی

ارسال می‌کند.

دستورات موجود:
/start - شروع و عضویت
/subscribe - عضویت در پیام‌های روزانه
/unsubscribe - لغو عضویت
/status - وضعیت عضویت
/help - راهنما

برای شروع دریافت پیام‌های انگیزشی روی /subscribe کلیک کنید.
        """
        
        keyboard = [
            [InlineKeyboardButton("🔔 عضویت در پیام‌های روزانه", callback_data="subscribe")],
            [InlineKeyboardButton("📊 آمار ربات", callback_data="stats")],
            [InlineKeyboardButton("❓ راهنما", callback_data="help")]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        await update.message.reply_text(welcome_text, reply_markup=reply_markup)
    
    async def subscribe_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /subscribe command"""
        chat_id = update.effective_chat.id
        user = update.effective_user
        
        success = self.db.add_user(
            chat_id=chat_id,
            username=user.username,
            first_name=user.first_name,
            last_name=user.last_name
        )
        
        if success:
            text = """
✅ شما با موفقیت در لیست دریافت پیام‌های انگیزشی قرار گرفتید!

🕐 هر روز ساعت 8 صبح پیام انگیزشی دریافت خواهید کرد.

برای لغو عضویت از دستور /unsubscribe استفاده کنید.
            """
        else:
            text = "❌ خطا در ثبت عضویت. لطفاً دوباره تلاش کنید."
        
        await update.message.reply_text(text)
    
    async def unsubscribe_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /unsubscribe command"""
        chat_id = update.effective_chat.id
        
        success = self.db.deactivate_user(chat_id)
        
        if success:
            text = """
❌ شما از لیست دریافت پیام‌های انگیزشی حذف شدید.

برای عضویت مجدد از دستور /subscribe استفاده کنید.
            """
        else:
            text = "❌ خطا در لغو عضویت. لطفاً دوباره تلاش کنید."
        
        await update.message.reply_text(text)
    
    async def status_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /status command"""
        chat_id = update.effective_chat.id
        active_users = self.db.get_active_users()
        
        if chat_id in active_users:
            status = "✅ فعال"
            next_message = "فردا ساعت 8 صبح"
        else:
            status = "❌ غیرفعال"
            next_message = "عضو نیستید"
        
        total_users = self.db.get_user_count()
        
        text = f"""
📊 وضعیت شما: {status}
⏰ پیام بعدی: {next_message}
👥 تعداد کل اعضا: {total_users}
        """
        
        await update.message.reply_text(text)
    
    async def help_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /help command"""
        help_text = """
📖 راهنمای استفاده از ربات انگیزشی

🔹 دستورات اصلی:
/start - شروع کار با ربات
/subscribe - عضویت در پیام‌های روزانه
/unsubscribe - لغو عضویت
/status - بررسی وضعیت عضویت
/help - نمایش این راهنما

🔹 ویژگی‌ها:
• ارسال پیام انگیزشی هر روز ساعت 8 صبح
• انواع محتوا: متن، عکس، ویدئو، موسیقی
• امکان عضویت و لغو عضویت آسان
• پیگیری آمار و وضعیت

🔹 نحوه کار:
1. روی /subscribe کلیک کنید
2. هر روز ساعت 8 صبح پیام انگیزشی دریافت کنید
3. در صورت تمایل با /unsubscribe عضویت را لغو کنید

💡 نکته: تمام پیام‌ها به صورت خودکار ارسال می‌شوند.
        """
        
        await update.message.reply_text(help_text)
    
    async def button_callback(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle inline keyboard callbacks"""
        query = update.callback_query
        await query.answer()

        chat_id = query.message.chat_id
        user = query.from_user

        if query.data == "subscribe":
            success = self.db.add_user(
                chat_id=chat_id,
                username=user.username,
                first_name=user.first_name,
                last_name=user.last_name
            )

            if success:
                text = "✅ شما با موفقیت عضو شدید! هر روز ساعت 8 صبح پیام انگیزشی دریافت خواهید کرد."
            else:
                text = "❌ خطا در ثبت عضویت. لطفاً دوباره تلاش کنید."

            await query.edit_message_text(text)

        elif query.data == "stats":
            active_users = self.db.get_active_users()
            total_users = self.db.get_user_count()

            if chat_id in active_users:
                status = "✅ فعال"
            else:
                status = "❌ غیرفعال"

            text = f"""
📊 آمار ربات:
وضعیت شما: {status}
👥 تعداد کل اعضا: {total_users}
⏰ زمان ارسال: هر روز ساعت 8 صبح
            """
            await query.edit_message_text(text)

        elif query.data == "help":
            await self.help_command(update, context)
    
    async def send_daily_message(self):
        """Send daily message from channel content to database users"""
        from datetime import datetime

        active_users = self.db.get_active_users()
        if not active_users:
            logger.info("No active users to send messages to")
            return

        # Get next unused message from channel tracker
        unused_message = self.channel_tracker.get_unused_message()
        if not unused_message:
            logger.warning("No unused messages available in channel tracker")
            return

        message_id, tracked_channel_id = unused_message
        logger.info(f"📤 Sending message based on channel message {message_id} to {len(active_users)} users")

        # Get message content (for now using predefined content based on message ID)
        # In future, you can implement reading actual channel messages
        sample_messages = {
            248: "🌟 موفقیت نتیجه تلاش‌های کوچک روزانه است که هر روز تکرار می‌شوند.",
            249: "🏔️ کوه‌ها از سنگ‌های کوچک ساخته می‌شوند. هر قدم کوچک شما به سمت قله‌ای بزرگ است.",
            250: "💪 قدرت واقعی در این نیست که هرگز نیفتید، بلکه در این است که هر بار بلند شوید.",
            251: "🌅 هر روز فرصت جدیدی است برای شروع دوباره. امروز را با انگیزه آغاز کنید.",
            252: "🎯 هدف‌هایتان را واضح تعریف کنید و هر روز قدمی به سمت آن‌ها بردارید."
        }

        content_text = sample_messages.get(message_id, f"پیام انگیزشی شماره {message_id}")

        # Create beautiful message format
        current_time = datetime.now()
        time_str = current_time.strftime("%H:%M")
        date_str = current_time.strftime("%Y/%m/%d")
        day_name = current_time.strftime("%A")

        # Persian day names
        persian_days = {
            'Monday': 'دوشنبه', 'Tuesday': 'سه‌شنبه', 'Wednesday': 'چهارشنبه',
            'Thursday': 'پنج‌شنبه', 'Friday': 'جمعه', 'Saturday': 'شنبه', 'Sunday': 'یکشنبه'
        }
        persian_day = persian_days.get(day_name, day_name)

        message_text = f"""🌟 پیام انگیزشی روزانه

{content_text}

━━━━━━━━━━━━━━━━━━━━
📅 {persian_day} | {date_str}
🕐 {time_str}
💫 از کانال انگیزشی واحد

#انگیزشی #موفقیت #روزانه"""

        try:
            success_count = 0
            failed_count = 0

            # Send message to all database users
            for chat_id in active_users:
                try:
                    await self.application.bot.send_message(
                        chat_id=chat_id,
                        text=message_text,
                        parse_mode='HTML'
                    )
                    success_count += 1
                    logger.info(f"Daily message sent to user {chat_id}")
                    await asyncio.sleep(0.1)  # Rate limiting

                except Exception as e:
                    logger.error(f"Failed to send daily message to user {chat_id}: {e}")
                    failed_count += 1

                    # Handle specific errors
                    if "bot was blocked" in str(e).lower():
                        logger.info(f"🚫 User {chat_id} blocked the bot - deactivating")
                        self.db.deactivate_user(chat_id)
                    elif "chat not found" in str(e).lower():
                        logger.info(f"👻 User {chat_id} not found - deactivating")
                        self.db.deactivate_user(chat_id)

            # Mark message as used if at least one send was successful
            if success_count > 0:
                self.channel_tracker.mark_message_used(message_id)
                logger.info(f"✅ Marked message {message_id} as used")

            logger.info(f"Daily message sending completed: {success_count} success, {failed_count} failed")

        except Exception as e:
            logger.error(f"Error in send_daily_message: {e}")
    
    async def run(self):
        """Run the bot"""
        self.application = Application.builder().token(self.token).build()
        
        # Add handlers
        self.application.add_handler(CommandHandler("start", self.start_command))
        self.application.add_handler(CommandHandler("subscribe", self.subscribe_command))
        self.application.add_handler(CommandHandler("unsubscribe", self.unsubscribe_command))
        self.application.add_handler(CommandHandler("status", self.status_command))
        self.application.add_handler(CommandHandler("help", self.help_command))
        self.application.add_handler(CallbackQueryHandler(self.button_callback))
        
        # Start scheduler
        await self.scheduler.start()
        
        # Start bot
        await self.application.initialize()
        await self.application.start()
        await self.application.updater.start_polling()
        
        logger.info("Bot started successfully!")
        
        # Keep running
        try:
            await asyncio.Event().wait()
        except KeyboardInterrupt:
            logger.info("Stopping bot...")
        finally:
            await self.scheduler.stop()
            await self.application.stop()
            await self.application.shutdown()
