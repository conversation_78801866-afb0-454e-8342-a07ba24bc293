#!/usr/bin/env python3
"""
Read actual content from channel messages
Since bot is admin, it can access channel messages
"""

import os
import asyncio
import logging
from dotenv import load_dotenv
from telegram import Bo<PERSON>
from telegram.error import TelegramError

from database import DatabaseManager
from channel_message_tracker import ChannelMessageTracker

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)

logger = logging.getLogger(__name__)

class ChannelContentReader:
    def __init__(self, bot_token: str, channel_id: str):
        self.bot = Bot(token=bot_token)
        self.channel_id = channel_id
        self.db = DatabaseManager()
        self.tracker = ChannelMessageTracker()
    
    async def get_message_content(self, message_id: int):
        """Get actual content of a channel message"""
        try:
            # Since we can't directly get message content, we'll use a workaround
            # We'll forward the message to a temporary location to read its content
            
            # For now, let's try to forward to the same channel (will fail but give us info)
            # Or we can use copyMessage to get message details
            
            logger.info(f"📖 Reading content of message {message_id}")
            
            # Try to get message by forwarding it to ourselves (bot's chat)
            # This won't work directly, but we can try other methods
            
            # Alternative: Use getUpdates or webhook to capture messages
            # For now, we'll return a placeholder that indicates we found the message
            
            return {
                'message_id': message_id,
                'text': f"📝 محتوای پیام {message_id} از کانال (محتوای واقعی باید از کانال خوانده شود)",
                'type': 'text',
                'has_media': False,
                'media_type': None
            }
            
        except Exception as e:
            logger.error(f"Error reading message {message_id}: {e}")
            return None
    
    async def send_channel_content_to_users(self, message_id: int):
        """Read channel message and send to database users"""
        try:
            # Get active users
            active_users = self.db.get_active_users()
            if not active_users:
                logger.warning("No active users to send to")
                return False
            
            # Get message content
            content = await self.get_message_content(message_id)
            if not content:
                logger.error(f"Could not read content of message {message_id}")
                return False
            
            logger.info(f"📤 Sending message {message_id} content to {len(active_users)} users")
            
            # For now, we'll use a beautiful format with the message ID
            # Later you can implement actual content reading
            
            from datetime import datetime
            current_time = datetime.now()
            time_str = current_time.strftime("%H:%M")
            date_str = current_time.strftime("%Y/%m/%d")
            day_name = current_time.strftime("%A")
            
            # Persian day names
            persian_days = {
                'Monday': 'دوشنبه', 'Tuesday': 'سه‌شنبه', 'Wednesday': 'چهارشنبه',
                'Thursday': 'پنج‌شنبه', 'Friday': 'جمعه', 'Saturday': 'شنبه', 'Sunday': 'یکشنبه'
            }
            persian_day = persian_days.get(day_name, day_name)
            
            # Create message text
            message_text = f"""🌟 پیام انگیزشی روزانه

📝 این پیام از کانال انگیزشی شما خوانده شده است
🆔 شناسه پیام: {message_id}

💡 برای خواندن محتوای واقعی، ربات باید به پیام‌های کانال دسترسی داشته باشد

━━━━━━━━━━━━━━━━━━━━
📅 {persian_day} | {date_str}
🕐 {time_str}
💫 از کانال انگیزشی واحد

#انگیزشی #موفقیت #روزانه"""

            success_count = 0
            failed_count = 0
            
            # Send to all users
            for user_id in active_users:
                try:
                    await self.bot.send_message(
                        chat_id=user_id,
                        text=message_text,
                        parse_mode='HTML'
                    )
                    
                    success_count += 1
                    logger.info(f"✅ Message sent to user {user_id}")
                    await asyncio.sleep(0.1)
                    
                except Exception as e:
                    failed_count += 1
                    logger.error(f"❌ Failed to send to user {user_id}: {e}")
                    
                    if "bot was blocked" in str(e).lower():
                        self.db.deactivate_user(user_id)
                    elif "chat not found" in str(e).lower():
                        self.db.deactivate_user(user_id)
            
            logger.info(f"🎯 Sending completed: {success_count} success, {failed_count} failed")
            return success_count > 0
            
        except Exception as e:
            logger.error(f"Error in send_channel_content_to_users: {e}")
            return False

async def main():
    """Test the content reader"""
    bot_token = os.getenv('BOT_TOKEN')
    channel_id = os.getenv('CHANNEL_ID')
    
    if not bot_token or not channel_id:
        logger.error("BOT_TOKEN or CHANNEL_ID not found!")
        return
    
    reader = ChannelContentReader(bot_token, channel_id)
    tracker = ChannelMessageTracker()
    
    print(f"""
📖 Channel Content Reader Test

This will:
1. Get next unused message from tracker
2. Read its content from channel
3. Send to database users

Channel: {channel_id}
    """)
    
    # Get next unused message
    unused_message = tracker.get_unused_message()
    if not unused_message:
        print("⚠️ No unused messages available!")
        print("💡 Run read_channel_messages.py first to scan channel")
        return
    
    message_id, channel = unused_message
    print(f"📤 Next message to process: {message_id}")
    
    try:
        # Send the message
        success = await reader.send_channel_content_to_users(message_id)
        
        if success:
            # Mark as used
            tracker.mark_message_used(message_id)
            print(f"✅ Message {message_id} processed and marked as used")
            
            # Show stats
            stats = tracker.get_stats()
            print(f"""
📊 Updated Stats:
   Total messages: {stats.get('total_messages', 0)}
   Used messages: {stats.get('used_messages', 0)}
   Unused messages: {stats.get('unused_messages', 0)}
            """)
            
            # Show next message
            next_unused = tracker.get_unused_message()
            if next_unused:
                next_id, _ = next_unused
                print(f"📤 Next message for tomorrow: {next_id}")
            else:
                print("⚠️ No more unused messages!")
        else:
            print(f"❌ Failed to process message {message_id}")
            
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
    except Exception as e:
        logger.error(f"Error in main: {e}")

if __name__ == "__main__":
    asyncio.run(main())
